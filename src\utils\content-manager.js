// Dynamic Content Management System

class ContentManager {
  constructor() {
    this.cache = new Map();
    this.observers = [];
    this.contentSources = {
      local: 'content',
      api: '/api/content',
      cms: process.env.CMS_ENDPOINT || null
    };
    this.version = '1.0.0';
    this.init();
  }

  init() {
    this.setupContentObserver();
    this.loadContentManifest();
  }

  // Content Loading Methods
  async loadContent(type, id, options = {}) {
    const cacheKey = `${type}-${id}`;
    
    // Check cache first
    if (this.cache.has(cacheKey) && !options.forceRefresh) {
      return this.cache.get(cacheKey);
    }

    try {
      let content;
      
      // Try different sources in order of preference
      if (this.contentSources.cms) {
        content = await this.loadFromCMS(type, id);
      } else if (this.contentSources.api) {
        content = await this.loadFromAPI(type, id);
      } else {
        content = await this.loadFromLocal(type, id);
      }

      // Cache the content
      this.cache.set(cacheKey, content);
      
      // Notify observers
      this.notifyObservers('contentLoaded', { type, id, content });
      
      return content;
    } catch (error) {
      console.error(`Failed to load content ${type}/${id}:`, error);
      
      // Try fallback to cached version
      if (this.cache.has(cacheKey)) {
        return this.cache.get(cacheKey);
      }
      
      throw error;
    }
  }

  async loadFromLocal(type, id) {
    const response = await fetch(`/content/${type}/${id}.json`);
    if (!response.ok) {
      throw new Error(`Failed to load local content: ${response.status}`);
    }
    return response.json();
  }

  async loadFromAPI(type, id) {
    const response = await fetch(`${this.contentSources.api}/${type}/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to load API content: ${response.status}`);
    }
    return response.json();
  }

  async loadFromCMS(type, id) {
    const response = await fetch(`${this.contentSources.cms}/${type}/${id}`, {
      headers: {
        'Authorization': `Bearer ${process.env.CMS_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to load CMS content: ${response.status}`);
    }
    
    return response.json();
  }

  // Content Collection Methods
  async loadCollection(type, options = {}) {
    const {
      limit = 10,
      offset = 0,
      sortBy = 'date',
      sortOrder = 'desc',
      filter = {},
      search = ''
    } = options;

    try {
      let collection;
      
      if (this.contentSources.cms) {
        collection = await this.loadCollectionFromCMS(type, options);
      } else if (this.contentSources.api) {
        collection = await this.loadCollectionFromAPI(type, options);
      } else {
        collection = await this.loadCollectionFromLocal(type, options);
      }

      // Apply client-side filtering if needed
      if (search) {
        collection.items = this.filterBySearch(collection.items, search);
      }

      return collection;
    } catch (error) {
      console.error(`Failed to load collection ${type}:`, error);
      throw error;
    }
  }

  async loadCollectionFromLocal(type, options) {
    const response = await fetch(`/content/${type}/index.json`);
    if (!response.ok) {
      throw new Error(`Failed to load local collection: ${response.status}`);
    }
    
    const data = await response.json();
    return this.applyCollectionOptions(data, options);
  }

  async loadCollectionFromAPI(type, options) {
    const params = new URLSearchParams(options);
    const response = await fetch(`${this.contentSources.api}/${type}?${params}`);
    
    if (!response.ok) {
      throw new Error(`Failed to load API collection: ${response.status}`);
    }
    
    return response.json();
  }

  async loadCollectionFromCMS(type, options) {
    const params = new URLSearchParams(options);
    const response = await fetch(`${this.contentSources.cms}/${type}?${params}`, {
      headers: {
        'Authorization': `Bearer ${process.env.CMS_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to load CMS collection: ${response.status}`);
    }
    
    return response.json();
  }

  // Content Manipulation Methods
  applyCollectionOptions(data, options) {
    let items = [...data.items];
    
    // Apply sorting
    if (options.sortBy) {
      items.sort((a, b) => {
        const aVal = a[options.sortBy];
        const bVal = b[options.sortBy];
        
        if (options.sortOrder === 'desc') {
          return bVal > aVal ? 1 : -1;
        } else {
          return aVal > bVal ? 1 : -1;
        }
      });
    }
    
    // Apply filtering
    if (options.filter && Object.keys(options.filter).length > 0) {
      items = items.filter(item => {
        return Object.entries(options.filter).every(([key, value]) => {
          return item[key] === value;
        });
      });
    }
    
    // Apply pagination
    const total = items.length;
    const start = options.offset || 0;
    const end = start + (options.limit || items.length);
    items = items.slice(start, end);
    
    return {
      items,
      total,
      offset: start,
      limit: options.limit || items.length,
      hasMore: end < total
    };
  }

  filterBySearch(items, search) {
    const searchLower = search.toLowerCase();
    return items.filter(item => {
      const searchableFields = ['title', 'description', 'content', 'tags'];
      return searchableFields.some(field => {
        const value = item[field];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchLower);
        } else if (Array.isArray(value)) {
          return value.some(v => 
            typeof v === 'string' && v.toLowerCase().includes(searchLower)
          );
        }
        return false;
      });
    });
  }

  // Content Versioning
  async loadContentVersion(type, id, version) {
    const cacheKey = `${type}-${id}-v${version}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    try {
      const response = await fetch(`/content/${type}/${id}/versions/${version}.json`);
      if (!response.ok) {
        throw new Error(`Version ${version} not found`);
      }
      
      const content = await response.json();
      this.cache.set(cacheKey, content);
      
      return content;
    } catch (error) {
      console.error(`Failed to load content version:`, error);
      throw error;
    }
  }

  async getContentVersions(type, id) {
    try {
      const response = await fetch(`/content/${type}/${id}/versions/index.json`);
      if (!response.ok) {
        return [];
      }
      
      const versions = await response.json();
      return versions.sort((a, b) => new Date(b.date) - new Date(a.date));
    } catch (error) {
      console.error(`Failed to load content versions:`, error);
      return [];
    }
  }

  // Content Manifest
  async loadContentManifest() {
    try {
      const response = await fetch('/content/manifest.json');
      if (response.ok) {
        this.manifest = await response.json();
        this.notifyObservers('manifestLoaded', this.manifest);
      }
    } catch (error) {
      console.error('Failed to load content manifest:', error);
    }
  }

  getContentTypes() {
    return this.manifest?.types || ['projects', 'blog', 'skills'];
  }

  // Real-time Content Updates
  setupContentObserver() {
    if ('serviceWorker' in navigator && 'MessageChannel' in window) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data.type === 'CONTENT_UPDATED') {
          this.handleContentUpdate(event.data.payload);
        }
      });
    }
  }

  handleContentUpdate(payload) {
    const { type, id, action } = payload;
    const cacheKey = `${type}-${id}`;
    
    switch (action) {
      case 'updated':
        // Invalidate cache
        this.cache.delete(cacheKey);
        this.notifyObservers('contentUpdated', payload);
        break;
      case 'deleted':
        this.cache.delete(cacheKey);
        this.notifyObservers('contentDeleted', payload);
        break;
      case 'created':
        this.notifyObservers('contentCreated', payload);
        break;
    }
  }

  // Observer Pattern
  addObserver(callback) {
    this.observers.push(callback);
  }

  removeObserver(callback) {
    const index = this.observers.indexOf(callback);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }

  notifyObservers(event, data) {
    this.observers.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Content observer error:', error);
      }
    });
  }

  // Cache Management
  clearCache(pattern = null) {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const [key] of this.cache) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // Preloading
  async preloadContent(contentList) {
    const promises = contentList.map(({ type, id }) => 
      this.loadContent(type, id).catch(error => 
        console.warn(`Failed to preload ${type}/${id}:`, error)
      )
    );
    
    await Promise.allSettled(promises);
  }
}

export default ContentManager;

// Auto-initialize if in browser environment
if (typeof window !== 'undefined') {
  window.contentManager = new ContentManager();
}
