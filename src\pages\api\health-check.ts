import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ request }) => {
  const startTime = Date.now();
  
  try {
    // Basic health checks
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: import.meta.env.MODE,
      version: '1.0.0',
      checks: {
        server: 'ok',
        memory: 'ok',
        responseTime: 0
      }
    };

    // Memory usage check
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const memUsage = process.memoryUsage();
      const memUsageMB = {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      };

      healthData.checks.memory = memUsageMB.heapUsed > 512 ? 'warning' : 'ok';
      (healthData as any).memory = memUsageMB;
    }

    // Response time calculation
    const responseTime = Date.now() - startTime;
    healthData.checks.responseTime = responseTime;

    // Overall status determination
    const hasWarnings = Object.values(healthData.checks).some(check => 
      check === 'warning' || (typeof check === 'number' && check > 1000)
    );
    
    if (hasWarnings) {
      healthData.status = 'degraded';
    }

    // Set appropriate status code
    const statusCode = healthData.status === 'healthy' ? 200 : 
                      healthData.status === 'degraded' ? 200 : 503;

    return new Response(JSON.stringify(healthData, null, 2), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    const errorResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      checks: {
        server: 'error',
        memory: 'unknown',
        responseTime: Date.now() - startTime
      }
    };

    return new Response(JSON.stringify(errorResponse, null, 2), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
};
