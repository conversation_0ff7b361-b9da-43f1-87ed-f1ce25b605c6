---
export interface Props {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'floating';
  showLabel?: boolean;
  compact?: boolean;
}

const { 
  position = 'top-right',
  showLabel = true,
  compact = false
} = Astro.props;
---

<div class={`theme-switcher theme-switcher--${position} ${compact ? 'theme-switcher--compact' : ''}`}>
  {showLabel && !compact && (
    <span class="theme-switcher__label">Theme</span>
  )}
  
  <button 
    class="theme-switcher__button"
    id="themeSwitcherButton"
    aria-label="Switch theme"
    aria-expanded="false"
    aria-haspopup="true"
    title="Change color theme (Ctrl+Shift+T)"
  >
    <svg class="theme-switcher__icon" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path 
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" 
        fill="currentColor"
      />
      <path 
        d="M12 6v12l6-6-6-6z" 
        fill="currentColor" 
        opacity="0.7"
      />
    </svg>
    
    {compact && (
      <span class="sr-only">Switch Theme</span>
    )}
  </button>

  <div class="theme-switcher__dropdown" id="themeSwitcherDropdown" role="menu" aria-labelledby="themeSwitcherButton">
    <div class="theme-switcher__options">
      <button 
        class="theme-option" 
        data-theme="cyberpunk" 
        role="menuitem"
        aria-label="Cyberpunk theme"
      >
        <div class="theme-option__preview theme-option__preview--cyberpunk"></div>
        <span class="theme-option__name">Cyberpunk</span>
        <div class="theme-option__colors">
          <span class="color-dot" style="background: #00ffff;"></span>
          <span class="color-dot" style="background: #ff00ff;"></span>
          <span class="color-dot" style="background: #00ff00;"></span>
        </div>
      </button>

      <button 
        class="theme-option" 
        data-theme="neon" 
        role="menuitem"
        aria-label="Neon Dreams theme"
      >
        <div class="theme-option__preview theme-option__preview--neon"></div>
        <span class="theme-option__name">Neon Dreams</span>
        <div class="theme-option__colors">
          <span class="color-dot" style="background: #ff0080;"></span>
          <span class="color-dot" style="background: #8000ff;"></span>
          <span class="color-dot" style="background: #00ff80;"></span>
        </div>
      </button>

      <button 
        class="theme-option" 
        data-theme="matrix" 
        role="menuitem"
        aria-label="Matrix Code theme"
      >
        <div class="theme-option__preview theme-option__preview--matrix"></div>
        <span class="theme-option__name">Matrix Code</span>
        <div class="theme-option__colors">
          <span class="color-dot" style="background: #00ff00;"></span>
          <span class="color-dot" style="background: #008000;"></span>
          <span class="color-dot" style="background: #40ff40;"></span>
        </div>
      </button>

      <button 
        class="theme-option" 
        data-theme="minimal" 
        role="menuitem"
        aria-label="Minimal Dark theme"
      >
        <div class="theme-option__preview theme-option__preview--minimal"></div>
        <span class="theme-option__name">Minimal Dark</span>
        <div class="theme-option__colors">
          <span class="color-dot" style="background: #ffffff;"></span>
          <span class="color-dot" style="background: #888888;"></span>
          <span class="color-dot" style="background: #007acc;"></span>
        </div>
      </button>
    </div>
  </div>
</div>

<style>
  .theme-switcher {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 1000;
  }

  .theme-switcher--top-right {
    position: fixed;
    top: 1rem;
    right: 1rem;
  }

  .theme-switcher--top-left {
    position: fixed;
    top: 1rem;
    left: 1rem;
  }

  .theme-switcher--bottom-right {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
  }

  .theme-switcher--bottom-left {
    position: fixed;
    bottom: 1rem;
    left: 1rem;
  }

  .theme-switcher--floating {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 50px;
    padding: 0.75rem;
    box-shadow: var(--shadow-card);
    backdrop-filter: blur(10px);
  }

  .theme-switcher__label {
    color: var(--color-text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
  }

  .theme-switcher__button {
    background: var(--color-surface);
    border: 2px solid var(--color-border);
    border-radius: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--color-text);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .theme-switcher__button:hover,
  .theme-switcher__button:focus {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  .theme-switcher__button[aria-expanded="true"] {
    border-color: var(--color-primary);
    background: rgba(var(--color-primary), 0.1);
  }

  .theme-switcher__icon {
    width: 24px;
    height: 24px;
    transition: transform 0.3s ease;
  }

  .theme-switcher__button[aria-expanded="true"] .theme-switcher__icon {
    transform: rotate(180deg);
  }

  .theme-switcher__dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background: var(--color-surface);
    border: 2px solid var(--color-border);
    border-radius: 0.75rem;
    padding: 1rem;
    min-width: 280px;
    box-shadow: var(--shadow-card);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
  }

  .theme-switcher__dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .theme-switcher__options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .theme-option {
    background: transparent;
    border: 1px solid var(--color-border);
    border-radius: 0.5rem;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-align: left;
    color: var(--color-text);
    width: 100%;
  }

  .theme-option:hover,
  .theme-option:focus {
    border-color: var(--color-primary);
    background: rgba(var(--color-primary), 0.1);
    transform: translateX(4px);
  }

  .theme-option.active {
    border-color: var(--color-primary);
    background: rgba(var(--color-primary), 0.2);
    box-shadow: 0 0 10px rgba(var(--color-primary), 0.3);
  }

  .theme-option__preview {
    width: 32px;
    height: 32px;
    border-radius: 0.375rem;
    border: 1px solid var(--color-border);
    flex-shrink: 0;
  }

  .theme-option__preview--cyberpunk {
    background: linear-gradient(45deg, #00ffff, #ff00ff);
  }

  .theme-option__preview--neon {
    background: linear-gradient(45deg, #ff0080, #8000ff);
  }

  .theme-option__preview--matrix {
    background: linear-gradient(45deg, #00ff00, #008000);
  }

  .theme-option__preview--minimal {
    background: linear-gradient(45deg, #ffffff, #888888);
  }

  .theme-option__name {
    font-weight: 500;
    flex: 1;
  }

  .theme-option__colors {
    display: flex;
    gap: 0.25rem;
  }

  .color-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .theme-switcher--compact .theme-switcher__button {
    padding: 0.375rem;
  }

  .theme-switcher--compact .theme-switcher__icon {
    width: 20px;
    height: 20px;
  }

  .theme-switcher--compact .theme-switcher__dropdown {
    min-width: 240px;
  }

  /* Screen reader only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .theme-switcher--top-right,
    .theme-switcher--top-left {
      top: 0.5rem;
    }

    .theme-switcher--top-right,
    .theme-switcher--bottom-right {
      right: 0.5rem;
    }

    .theme-switcher--top-left,
    .theme-switcher--bottom-left {
      left: 0.5rem;
    }

    .theme-switcher--bottom-right,
    .theme-switcher--bottom-left {
      bottom: 0.5rem;
    }

    .theme-switcher__dropdown {
      min-width: 260px;
      right: auto;
      left: 0;
    }

    .theme-switcher--top-right .theme-switcher__dropdown,
    .theme-switcher--bottom-right .theme-switcher__dropdown {
      right: 0;
      left: auto;
    }
  }

  @media (max-width: 480px) {
    .theme-switcher__dropdown {
      min-width: 240px;
    }

    .theme-option {
      padding: 0.5rem;
      gap: 0.5rem;
    }

    .theme-option__preview {
      width: 28px;
      height: 28px;
    }

    .theme-option__name {
      font-size: 0.875rem;
    }
  }
</style>

<script>
  class ThemeSwitcherComponent {
    constructor() {
      this.button = document.getElementById('themeSwitcherButton');
      this.dropdown = document.getElementById('themeSwitcherDropdown');
      this.options = document.querySelectorAll('.theme-option');
      this.isOpen = false;
      
      this.init();
    }

    init() {
      if (!this.button || !this.dropdown) return;

      this.button.addEventListener('click', () => this.toggle());
      
      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (!this.button.contains(e.target) && !this.dropdown.contains(e.target)) {
          this.close();
        }
      });

      // Handle theme option clicks
      this.options.forEach(option => {
        option.addEventListener('click', (e) => {
          const theme = e.currentTarget.dataset.theme;
          this.selectTheme(theme);
          this.close();
        });
      });

      // Keyboard navigation
      this.button.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.toggle();
        } else if (e.key === 'Escape') {
          this.close();
        }
      });

      this.dropdown.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          this.close();
          this.button.focus();
        }
      });

      // Update active theme on load
      this.updateActiveTheme();
    }

    toggle() {
      if (this.isOpen) {
        this.close();
      } else {
        this.open();
      }
    }

    open() {
      this.isOpen = true;
      this.button.setAttribute('aria-expanded', 'true');
      this.dropdown.classList.add('show');
      
      // Focus first option
      const firstOption = this.dropdown.querySelector('.theme-option');
      if (firstOption) {
        firstOption.focus();
      }
    }

    close() {
      this.isOpen = false;
      this.button.setAttribute('aria-expanded', 'false');
      this.dropdown.classList.remove('show');
    }

    selectTheme(themeName) {
      if (window.themeSystem) {
        window.themeSystem.switchTheme(themeName);
        this.updateActiveTheme();
      }
    }

    updateActiveTheme() {
      const currentTheme = window.themeSystem?.currentTheme || 'cyberpunk';
      
      this.options.forEach(option => {
        const theme = option.dataset.theme;
        if (theme === currentTheme) {
          option.classList.add('active');
          option.setAttribute('aria-pressed', 'true');
        } else {
          option.classList.remove('active');
          option.setAttribute('aria-pressed', 'false');
        }
      });
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new ThemeSwitcherComponent();
    });
  } else {
    new ThemeSwitcherComponent();
  }
</script>
