---
export interface Props {
  showProgress?: boolean;
  theme?: 'cyberpunk' | 'minimal' | 'matrix';
}

const { 
  showProgress = true,
  theme = 'cyberpunk'
} = Astro.props;
---

<div class="loading-screen" data-theme={theme}>
  <div class="loading-container">
    <!-- Logo/Brand -->
    <div class="loading-logo">
      <div class="logo-text glitch" data-text="MT">MT</div>
      <div class="logo-subtitle">Muhammad <PERSON>nan<PERSON></div>
    </div>

    <!-- Loading Animation -->
    <div class="loading-animation">
      <div class="cyber-loader">
        <div class="loader-ring"></div>
        <div class="loader-ring"></div>
        <div class="loader-ring"></div>
      </div>
    </div>

    <!-- Progress Bar -->
    {showProgress && (
      <div class="loading-progress-container">
        <div class="loading-progress-bar">
          <div class="loading-progress"></div>
        </div>
        <div class="loading-percentage">0%</div>
      </div>
    )}

    <!-- Loading Text -->
    <div class="loading-text">
      <span class="loading-message">Initializing Portfolio...</span>
      <div class="loading-dots">
        <span>.</span>
        <span>.</span>
        <span>.</span>
      </div>
    </div>
  </div>

  <!-- Background Effects -->
  <div class="loading-bg-effects">
    <div class="grid-overlay"></div>
    <div class="scan-lines"></div>
  </div>
</div>

<style>
  .loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    overflow: hidden;
  }

  .loading-container {
    text-align: center;
    position: relative;
    z-index: 2;
  }

  .loading-logo {
    margin-bottom: 3rem;
  }

  .logo-text {
    font-size: 4rem;
    font-weight: bold;
    background: linear-gradient(45deg, #00ffff, #ff00ff, #00ff00);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    margin-bottom: 0.5rem;
  }

  .logo-subtitle {
    color: #64ffda;
    font-size: 1.2rem;
    font-weight: 300;
    letter-spacing: 0.2em;
    opacity: 0.8;
  }

  .loading-animation {
    margin: 3rem 0;
  }

  .cyber-loader {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
  }

  .loader-ring {
    position: absolute;
    border: 2px solid transparent;
    border-radius: 50%;
    animation: spin 2s linear infinite;
  }

  .loader-ring:nth-child(1) {
    width: 120px;
    height: 120px;
    border-top-color: #00ffff;
    animation-duration: 2s;
  }

  .loader-ring:nth-child(2) {
    width: 90px;
    height: 90px;
    top: 15px;
    left: 15px;
    border-right-color: #ff00ff;
    animation-duration: 1.5s;
    animation-direction: reverse;
  }

  .loader-ring:nth-child(3) {
    width: 60px;
    height: 60px;
    top: 30px;
    left: 30px;
    border-bottom-color: #00ff00;
    animation-duration: 1s;
  }

  .loading-progress-container {
    margin: 2rem 0;
    width: 300px;
    margin-left: auto;
    margin-right: auto;
  }

  .loading-progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
  }

  .loading-progress {
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, #00ffff, #ff00ff);
    border-radius: 2px;
    transition: width 0.3s ease;
    position: relative;
  }

  .loading-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
  }

  .loading-percentage {
    color: #64ffda;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    font-family: 'Courier New', monospace;
  }

  .loading-text {
    margin-top: 2rem;
  }

  .loading-message {
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 300;
    letter-spacing: 0.1em;
  }

  .loading-dots {
    display: inline-block;
    margin-left: 0.5rem;
  }

  .loading-dots span {
    color: #00ffff;
    animation: blink 1.5s infinite;
  }

  .loading-dots span:nth-child(2) {
    animation-delay: 0.5s;
  }

  .loading-dots span:nth-child(3) {
    animation-delay: 1s;
  }

  .loading-bg-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
  }

  .scan-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      transparent 50%,
      rgba(0, 255, 255, 0.03) 50%
    );
    background-size: 100% 4px;
    animation: scanLines 0.1s linear infinite;
  }

  /* Animations */
  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }

  @keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
  }

  @keyframes scanLines {
    0% { transform: translateY(0); }
    100% { transform: translateY(4px); }
  }

  /* Glitch Effect */
  .glitch {
    position: relative;
  }

  .glitch::before,
  .glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch::before {
    animation: glitch-1 0.5s infinite;
    color: #ff00ff;
    z-index: -1;
  }

  .glitch::after {
    animation: glitch-2 0.5s infinite;
    color: #00ffff;
    z-index: -2;
  }

  @keyframes glitch-1 {
    0%, 14%, 15%, 49%, 50%, 99%, 100% {
      transform: translate(0);
    }
    15%, 49% {
      transform: translate(-2px, 2px);
    }
  }

  @keyframes glitch-2 {
    0%, 20%, 21%, 62%, 63%, 99%, 100% {
      transform: translate(0);
    }
    21%, 62% {
      transform: translate(2px, -2px);
    }
  }

  /* Theme Variations */
  .loading-screen[data-theme="minimal"] {
    background: #000;
  }

  .loading-screen[data-theme="matrix"] {
    background: #000;
  }

  .loading-screen[data-theme="matrix"] .logo-text {
    background: linear-gradient(45deg, #00ff00, #008000);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
</style>

<script>
  class LoadingManager {
    constructor() {
      this.progress = 0;
      this.messages = [
        "Initializing Portfolio...",
        "Loading Assets...",
        "Preparing 3D Environment...",
        "Setting up Animations...",
        "Almost Ready...",
        "Welcome!"
      ];
      this.currentMessageIndex = 0;
      this.init();
    }

    init() {
      this.updateProgress();
      this.updateMessage();
    }

    updateProgress() {
      const progressBar = document.querySelector('.loading-progress');
      const percentage = document.querySelector('.loading-percentage');
      
      const interval = setInterval(() => {
        this.progress += Math.random() * 15;
        
        if (this.progress >= 100) {
          this.progress = 100;
          clearInterval(interval);
          setTimeout(() => this.hideLoading(), 500);
        }
        
        if (progressBar) {
          progressBar.style.width = `${this.progress}%`;
        }
        
        if (percentage) {
          percentage.textContent = `${Math.floor(this.progress)}%`;
        }
        
        // Update message based on progress
        const messageIndex = Math.floor((this.progress / 100) * (this.messages.length - 1));
        if (messageIndex !== this.currentMessageIndex) {
          this.currentMessageIndex = messageIndex;
          this.updateMessage();
        }
      }, 100);
    }

    updateMessage() {
      const messageElement = document.querySelector('.loading-message');
      if (messageElement && this.messages[this.currentMessageIndex]) {
        messageElement.textContent = this.messages[this.currentMessageIndex];
      }
    }

    hideLoading() {
      const loadingScreen = document.querySelector('.loading-screen');
      if (loadingScreen) {
        loadingScreen.style.opacity = '0';
        loadingScreen.style.transition = 'opacity 0.5s ease';
        
        setTimeout(() => {
          loadingScreen.style.display = 'none';
        }, 500);
      }
    }
  }

  // Initialize loading manager when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new LoadingManager();
    });
  } else {
    new LoadingManager();
  }
</script>
