---
import { getCollection, getEntry, render } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import { Image } from "astro:assets";

export async function getStaticPaths() {
  const blogEntries = await getCollection("blog");
  return blogEntries.map((entry) => ({
    params: { slug: entry.id },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content } = await render(entry);

// Get related posts
const allPosts = await getCollection("blog");
const relatedPosts = allPosts
  .filter(
    (post) =>
      post.id !== entry.id &&
      !post.data.draft &&
      (post.data.category === entry.data.category ||
        post.data.tags?.some((tag) => entry.data.tags?.includes(tag)))
  )
  .slice(0, 3);

// Calculate reading progress
const wordCount = entry.body ? entry.body.split(/\s+/).length : 0;
const readingTime = Math.ceil(wordCount / 200);
---

<Layout
  title={entry.data.seo?.title || entry.data.title}
  description={entry.data.seo?.description || entry.data.description}
>
  <Header />

  <!-- Reading Progress Bar -->
  <div
    id="reading-progress"
    class="fixed top-0 left-0 w-full h-1 bg-gray-800 z-50"
  >
    <div
      class="h-full bg-gradient-to-r from-cyan-400 to-purple-500 transition-all duration-300"
      style="width: 0%"
    >
    </div>
  </div>

  <main class="min-h-screen pt-20 pb-16">
    <!-- Article Header -->
    <article class="max-w-4xl mx-auto px-6">
      <header class="text-center py-16 border-b border-gray-700 mb-12">
        <!-- Breadcrumb -->
        <nav class="text-sm text-gray-400 mb-6">
          <a href="/" class="hover:text-cyan-400 transition-colors">Home</a>
          <span class="mx-2">/</span>
          <a href="/blog" class="hover:text-cyan-400 transition-colors">Blog</a>
          <span class="mx-2">/</span>
          <span class="text-gray-300">{entry.data.title}</span>
        </nav>

        <!-- Category Badge -->
        <div class="mb-4">
          <span
            class="bg-cyan-500/20 text-cyan-400 px-3 py-1 rounded-full text-sm font-mono"
          >
            {entry.data.category}
          </span>
        </div>

        <!-- Title -->
        <h1
          class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent leading-tight"
        >
          {entry.data.title}
        </h1>

        <!-- Description -->
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed">
          {entry.data.description}
        </p>

        <!-- Meta Information -->
        <div
          class="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-400"
        >
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"></path>
            </svg>
            <span>{entry.data.author}</span>
          </div>
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                clip-rule="evenodd"></path>
            </svg>
            <time datetime={entry.data.publishedAt.toISOString()}>
              {
                entry.data.publishedAt.toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })
              }
            </time>
          </div>
          <div class="flex items-center gap-2">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                clip-rule="evenodd"></path>
            </svg>
            <span>{readingTime} min read</span>
          </div>
        </div>

        <!-- Tags -->
        {
          entry.data.tags && entry.data.tags.length > 0 && (
            <div class="flex flex-wrap justify-center gap-2 mt-6">
              {entry.data.tags.map((tag) => (
                <span class="bg-gray-700 text-gray-300 px-3 py-1 rounded-full text-sm hover:bg-gray-600 transition-colors">
                  #{tag}
                </span>
              ))}
            </div>
          )
        }
      </header>

      <!-- Featured Image -->
      {
        entry.data.image && (
          <div class="mb-12 rounded-lg overflow-hidden">
            <img
              src={entry.data.image}
              alt={entry.data.imageAlt || entry.data.title}
              class="w-full h-64 md:h-96 object-cover"
              loading="eager"
            />
          </div>
        )
      }

      <!-- Article Content -->
      <div class="prose prose-lg prose-invert max-w-none">
        <Content />
      </div>

      <!-- Article Footer -->
      <footer class="border-t border-gray-700 pt-12 mt-16">
        <!-- Share Buttons -->
        <div class="flex items-center justify-center gap-4 mb-8">
          <span class="text-gray-400 font-mono text-sm"
            >Share this article:</span
          >
          <div class="flex gap-3">
            <button
              class="share-btn bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded-lg transition-colors"
              data-platform="twitter"
              data-url={Astro.url.href}
              data-text={entry.data.title}
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"
                ></path>
              </svg>
            </button>
            <button
              class="share-btn bg-blue-600/20 hover:bg-blue-600/30 text-blue-400 p-2 rounded-lg transition-colors"
              data-platform="linkedin"
              data-url={Astro.url.href}
              data-text={entry.data.title}
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                ></path>
              </svg>
            </button>
            <button
              class="share-btn bg-gray-500/20 hover:bg-gray-500/30 text-gray-400 p-2 rounded-lg transition-colors"
              data-platform="copy"
              data-url={Astro.url.href}
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
                ></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Author Bio -->
        <div
          class="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 mb-8"
        >
          <div class="flex items-start gap-4">
            <div
              class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl"
            >
              MT
            </div>
            <div class="flex-1">
              <h3 class="text-xl font-semibold text-white mb-2">
                {entry.data.author}
              </h3>
              <p class="text-gray-300 text-sm mb-3">
                Full-stack developer passionate about creating innovative web
                experiences with modern technologies. Specializing in React,
                TypeScript, and cutting-edge web development.
              </p>
              <div class="flex gap-3">
                <a
                  href="https://twitter.com"
                  class="text-cyan-400 hover:text-cyan-300 transition-colors"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"
                    ></path>
                  </svg>
                </a>
                <a
                  href="https://github.com"
                  class="text-cyan-400 hover:text-cyan-300 transition-colors"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
                    ></path>
                  </svg>
                </a>
                <a
                  href="https://linkedin.com"
                  class="text-cyan-400 hover:text-cyan-300 transition-colors"
                >
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                    ></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </article>

    <!-- Related Posts -->
    {
      relatedPosts.length > 0 && (
        <section class="max-w-6xl mx-auto px-6 mt-16">
          <h2 class="text-2xl font-bold text-cyan-400 mb-8 text-center font-mono">
            Related Articles
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedPosts.map((post) => (
              <article class="group bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-cyan-500/50 transition-all duration-300">
                {post.data.image && (
                  <div class="aspect-video overflow-hidden">
                    <img
                      src={post.data.image}
                      alt={post.data.title}
                      class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                  </div>
                )}
                <div class="p-6">
                  <div class="flex items-center gap-2 mb-3">
                    <span class="bg-cyan-500/20 text-cyan-400 px-2 py-1 rounded text-xs font-mono">
                      {post.data.category}
                    </span>
                    <span class="text-gray-500 text-xs">
                      {new Date(post.data.publishedAt).toLocaleDateString()}
                    </span>
                  </div>
                  <h3 class="text-lg font-semibold text-white mb-3 group-hover:text-cyan-400 transition-colors">
                    <a href={`/blog/${post.id}`} class="hover:underline">
                      {post.data.title}
                    </a>
                  </h3>
                  <p class="text-gray-300 text-sm mb-4 line-clamp-3">
                    {post.data.description}
                  </p>
                  <div class="flex items-center justify-between">
                    <span class="text-gray-500 text-xs">
                      {post.data.readingTime} min read
                    </span>
                    <a
                      href={`/blog/${post.id}`}
                      class="text-cyan-400 hover:text-cyan-300 text-sm font-mono transition-colors"
                    >
                      Read more →
                    </a>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </section>
      )
    }

    <!-- Back to Blog -->
    <div class="text-center mt-16">
      <a
        href="/blog"
        class="inline-flex items-center gap-2 bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 px-6 py-3 rounded-lg font-mono transition-colors"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
            clip-rule="evenodd"></path>
        </svg>
        Back to Blog
      </a>
    </div>
  </main>

  <Footer />
</Layout>

<script>
  // Reading progress
  function updateReadingProgress() {
    const article = document.querySelector("article");
    const progressBar = document.querySelector("#reading-progress div");

    if (!article || !progressBar) return;

    const articleTop = article.offsetTop;
    const articleHeight = article.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.scrollY;

    const progress = Math.min(
      Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
      1
    );

    progressBar.style.width = `${progress * 100}%`;
  }

  // Share functionality
  function setupShareButtons() {
    document.querySelectorAll(".share-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const platform = e.currentTarget.dataset.platform;
        const url = e.currentTarget.dataset.url;
        const text = e.currentTarget.dataset.text;

        switch (platform) {
          case "twitter":
            window.open(
              `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
              "_blank"
            );
            break;
          case "linkedin":
            window.open(
              `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
              "_blank"
            );
            break;
          case "copy":
            navigator.clipboard.writeText(url).then(() => {
              // Show feedback
              const originalText = e.currentTarget.innerHTML;
              e.currentTarget.innerHTML =
                '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
              setTimeout(() => {
                e.currentTarget.innerHTML = originalText;
              }, 2000);
            });
            break;
        }
      });
    });
  }

  // Initialize
  document.addEventListener("DOMContentLoaded", () => {
    setupShareButtons();
    updateReadingProgress();
  });

  window.addEventListener("scroll", updateReadingProgress);
</script>

<style>
  .prose {
    @apply text-gray-300 leading-relaxed;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    @apply text-white font-bold mb-4 mt-8;
  }

  .prose h1 {
    @apply text-3xl;
  }
  .prose h2 {
    @apply text-2xl text-cyan-400;
  }
  .prose h3 {
    @apply text-xl text-purple-400;
  }

  .prose p {
    @apply mb-6;
  }

  .prose a {
    @apply text-cyan-400 hover:text-cyan-300 transition-colors underline;
  }

  .prose code {
    @apply bg-gray-800 text-cyan-400 px-2 py-1 rounded text-sm font-mono;
  }

  .prose pre {
    @apply bg-gray-900 border border-gray-700 rounded-lg p-4 overflow-x-auto mb-6;
  }

  .prose pre code {
    @apply bg-transparent p-0;
  }

  .prose blockquote {
    @apply border-l-4 border-cyan-500 pl-6 italic text-gray-400 my-6;
  }

  .prose ul,
  .prose ol {
    @apply mb-6 pl-6;
  }

  .prose li {
    @apply mb-2;
  }

  .prose img {
    @apply rounded-lg my-8;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
