---
// Enhanced Contact Form Component
---

<form id="contact-form" class="contact-form" novalidate>
  <div class="form-grid">
    <div class="form-group">
      <label for="name" class="form-label">
        <span class="label-text">Name</span>
        <span class="label-required">*</span>
      </label>
      <div class="input-wrapper">
        <input
          type="text"
          id="name"
          name="name"
          class="form-input"
          placeholder="Your full name"
          required
          autocomplete="name"
        />
        <div class="input-border"></div>
        <div class="error-message" id="name-error"></div>
      </div>
    </div>

    <div class="form-group">
      <label for="email" class="form-label">
        <span class="label-text">Email</span>
        <span class="label-required">*</span>
      </label>
      <div class="input-wrapper">
        <input
          type="email"
          id="email"
          name="email"
          class="form-input"
          placeholder="<EMAIL>"
          required
          autocomplete="email"
        />
        <div class="input-border"></div>
        <div class="error-message" id="email-error"></div>
      </div>
    </div>
  </div>

  <div class="form-group">
    <label for="subject" class="form-label">
      <span class="label-text">Subject</span>
      <span class="label-required">*</span>
    </label>
    <div class="input-wrapper">
      <select id="subject" name="subject" class="form-select" required>
        <option value="">Select a subject</option>
        <option value="collaboration">Collaboration Opportunity</option>
        <option value="project">Project Inquiry</option>
        <option value="consultation">Consultation</option>
        <option value="other">Other</option>
      </select>
      <div class="input-border"></div>
      <div class="error-message" id="subject-error"></div>
    </div>
  </div>

  <div class="form-group">
    <label for="message" class="form-label">
      <span class="label-text">Message</span>
      <span class="label-required">*</span>
    </label>
    <div class="input-wrapper">
      <textarea
        id="message"
        name="message"
        class="form-textarea"
        placeholder="Tell me about your project or inquiry..."
        rows="6"
        required
      ></textarea>
      <div class="input-border"></div>
      <div class="error-message" id="message-error"></div>
      <div class="character-count">
        <span id="char-count">0</span>/500
      </div>
    </div>
  </div>

  <div class="form-actions">
    <button type="submit" class="submit-btn" id="submit-btn">
      <span class="btn-text">Send Message</span>
      <span class="btn-loading">
        <svg class="loading-spinner" width="20" height="20" viewBox="0 0 24 24">
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="2"
            fill="none"
            stroke-linecap="round"
            stroke-dasharray="31.416"
            stroke-dashoffset="31.416"
          />
        </svg>
        Sending...
      </span>
      <span class="btn-success">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path
            d="M20 6L9 17L4 12"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        Sent!
      </span>
    </button>
  </div>

  <div class="form-status" id="form-status"></div>
</form>

<style>
  .contact-form {
    max-width: 600px;
    margin: 0 auto;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 24px;
  }

  .form-label {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
  }

  .label-required {
    color: #ff6b6b;
  }

  .input-wrapper {
    position: relative;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.3s ease;
    outline: none;
  }

  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    border-color: #00ffff;
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
  }

  .form-input.error,
  .form-select.error,
  .form-textarea.error {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  }

  .form-input.success,
  .form-select.success,
  .form-textarea.success {
    border-color: #51cf66;
    box-shadow: 0 0 0 3px rgba(81, 207, 102, 0.1);
  }

  .form-input::placeholder,
  .form-textarea::placeholder {
    color: #64748b;
  }

  .form-select {
    cursor: pointer;
  }

  .form-textarea {
    resize: vertical;
    min-height: 120px;
  }

  .input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #00ffff, #ff00ff);
    transition: width 0.3s ease;
  }

  .form-input:focus + .input-border,
  .form-select:focus + .input-border,
  .form-textarea:focus + .input-border {
    width: 100%;
  }

  .error-message {
    color: #ff6b6b;
    font-size: 12px;
    margin-top: 4px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
  }

  .error-message.show {
    opacity: 1;
    transform: translateY(0);
  }

  .character-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #64748b;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 32px;
  }

  .submit-btn {
    position: relative;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    border: none;
    border-radius: 8px;
    padding: 14px 32px;
    color: #000;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    min-width: 160px;
  }

  .submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
  }

  .submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .btn-text,
  .btn-loading,
  .btn-success {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
  }

  .btn-loading,
  .btn-success {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
  }

  .submit-btn.loading .btn-text {
    opacity: 0;
  }

  .submit-btn.loading .btn-loading {
    opacity: 1;
  }

  .submit-btn.success .btn-text,
  .submit-btn.success .btn-loading {
    opacity: 0;
  }

  .submit-btn.success .btn-success {
    opacity: 1;
  }

  .loading-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .form-status {
    margin-top: 20px;
    padding: 12px 16px;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
  }

  .form-status.show {
    opacity: 1;
    transform: translateY(0);
  }

  .form-status.success {
    background: rgba(81, 207, 102, 0.1);
    border: 1px solid rgba(81, 207, 102, 0.3);
    color: #51cf66;
  }

  .form-status.error {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
  }

  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
      font-size: 16px; /* Prevent zoom on iOS */
    }
  }
</style>

<script>
  class ContactFormHandler {
    constructor() {
      this.form = document.getElementById('contact-form');
      this.submitBtn = document.getElementById('submit-btn');
      this.status = document.getElementById('form-status');
      this.charCount = document.getElementById('char-count');
      
      this.validators = {
        name: (value) => value.trim().length >= 2,
        email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
        subject: (value) => value.trim().length > 0,
        message: (value) => value.trim().length >= 10 && value.trim().length <= 500
      };
      
      this.init();
    }

    init() {
      this.setupEventListeners();
      this.setupCharacterCounter();
    }

    setupEventListeners() {
      this.form?.addEventListener('submit', (e) => this.handleSubmit(e));
      
      // Real-time validation
      Object.keys(this.validators).forEach(field => {
        const input = document.getElementById(field);
        input?.addEventListener('blur', () => this.validateField(field));
        input?.addEventListener('input', () => this.clearFieldError(field));
      });
    }

    setupCharacterCounter() {
      const messageField = document.getElementById('message');
      messageField?.addEventListener('input', (e) => {
        const count = e.target.value.length;
        this.charCount.textContent = count;
        
        if (count > 500) {
          this.charCount.style.color = '#ff6b6b';
        } else if (count > 400) {
          this.charCount.style.color = '#ffa500';
        } else {
          this.charCount.style.color = '#64748b';
        }
      });
    }

    validateField(fieldName) {
      const input = document.getElementById(fieldName);
      const errorElement = document.getElementById(`${fieldName}-error`);
      const isValid = this.validators[fieldName](input.value);
      
      if (isValid) {
        input.classList.remove('error');
        input.classList.add('success');
        errorElement.classList.remove('show');
      } else {
        input.classList.remove('success');
        input.classList.add('error');
        errorElement.textContent = this.getErrorMessage(fieldName);
        errorElement.classList.add('show');
      }
      
      return isValid;
    }

    clearFieldError(fieldName) {
      const input = document.getElementById(fieldName);
      const errorElement = document.getElementById(`${fieldName}-error`);
      
      input.classList.remove('error');
      errorElement.classList.remove('show');
    }

    getErrorMessage(fieldName) {
      const messages = {
        name: 'Name must be at least 2 characters long',
        email: 'Please enter a valid email address',
        subject: 'Please select a subject',
        message: 'Message must be between 10 and 500 characters'
      };
      return messages[fieldName];
    }

    validateForm() {
      let isValid = true;
      Object.keys(this.validators).forEach(field => {
        if (!this.validateField(field)) {
          isValid = false;
        }
      });
      return isValid;
    }

    async handleSubmit(e) {
      e.preventDefault();
      
      if (!this.validateForm()) {
        this.showStatus('Please fix the errors above', 'error');
        return;
      }

      this.setLoading(true);
      
      try {
        const formData = new FormData(this.form);
        const data = Object.fromEntries(formData);
        
        // Simulate API call (replace with actual endpoint)
        await this.submitForm(data);
        
        this.setSuccess();
        this.showStatus('Message sent successfully! I\'ll get back to you soon.', 'success');
        this.form.reset();
        this.resetForm();
        
      } catch (error) {
        this.showStatus('Failed to send message. Please try again.', 'error');
      } finally {
        this.setLoading(false);
      }
    }

    async submitForm(data) {
      // Replace this with your actual form submission logic
      // For example, using Netlify Forms, Formspree, or your own API
      
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          // Simulate success/failure
          if (Math.random() > 0.1) {
            resolve();
          } else {
            reject(new Error('Submission failed'));
          }
        }, 2000);
      });
    }

    setLoading(loading) {
      if (loading) {
        this.submitBtn.classList.add('loading');
        this.submitBtn.disabled = true;
      } else {
        this.submitBtn.classList.remove('loading');
        this.submitBtn.disabled = false;
      }
    }

    setSuccess() {
      this.submitBtn.classList.add('success');
      setTimeout(() => {
        this.submitBtn.classList.remove('success');
      }, 3000);
    }

    showStatus(message, type) {
      this.status.textContent = message;
      this.status.className = `form-status show ${type}`;
      
      setTimeout(() => {
        this.status.classList.remove('show');
      }, 5000);
    }

    resetForm() {
      // Reset all field states
      Object.keys(this.validators).forEach(field => {
        const input = document.getElementById(field);
        input.classList.remove('success', 'error');
        
        const errorElement = document.getElementById(`${field}-error`);
        errorElement.classList.remove('show');
      });
      
      this.charCount.textContent = '0';
      this.charCount.style.color = '#64748b';
    }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ContactFormHandler();
  });
</script>
