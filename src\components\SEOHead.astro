---
export interface Props {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: "website" | "article" | "profile";
  publishedTime?: string;
  modifiedTime?: string;
  tags?: string[];
  author?: string;
  schema?: "Person" | "WebSite" | "Article" | "CreativeWork";
}

const {
  title,
  description,
  image = "/og-image.jpg",
  url = Astro.url.href,
  type = "website",
  publishedTime,
  modifiedTime,
  tags = [],
  author = "Muhammad Trinanda",
  schema = "WebSite",
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const imageURL = new URL(image, Astro.site);

// Generate structured data based on schema type
const generateStructuredData = () => {
  const baseData = {
    "@context": "https://schema.org",
    "@type": schema,
    name: title,
    description: description,
    url: url,
    image: imageURL.href,
  };

  switch (schema) {
    case "Person":
      return {
        ...baseData,
        "@type": "Person",
        jobTitle: "Data Analyst & Web Developer",
        worksFor: {
          "@type": "EducationalOrganization",
          name: "Universitas Islam Negeri Sumatera Utara",
        },
        alumniOf: {
          "@type": "EducationalOrganization",
          name: "Universitas Islam Negeri Sumatera Utara",
        },
        knowsAbout: [
          "Web Development",
          "Data Analysis",
          "Graphic Design",
          "Financial Analysis",
          "Sharia Accounting",
        ],
        sameAs: [
          "https://linkedin.com/in/muhammad-trinanda",
          "https://github.com/trinanda",
          "https://instagram.com/trinanda321",
        ],
      };

    case "Article":
      return {
        ...baseData,
        "@type": "Article",
        headline: title,
        author: {
          "@type": "Person",
          name: author,
        },
        publisher: {
          "@type": "Person",
          name: "Muhammad Trinanda",
        },
        datePublished: publishedTime,
        dateModified: modifiedTime || publishedTime,
        keywords: tags.join(", "),
      };

    case "CreativeWork":
      return {
        ...baseData,
        "@type": "CreativeWork",
        creator: {
          "@type": "Person",
          name: author,
        },
        dateCreated: publishedTime,
        keywords: tags.join(", "),
      };

    default:
      return {
        ...baseData,
        author: {
          "@type": "Person",
          name: author,
        },
      };
  }
};

const structuredData = generateStructuredData();
---

<!-- Primary Meta Tags -->
<title>{title}</title>
<meta name="title" content={title} />
<meta name="description" content={description} />
<meta name="author" content={author} />
<link rel="canonical" href={canonicalURL} />

<!-- Open Graph / Facebook -->
<meta property="og:type" content={type} />
<meta property="og:url" content={url} />
<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:image" content={imageURL} />
<meta property="og:image:alt" content={title} />
<meta property="og:site_name" content="Muhammad Trinanda Portfolio" />
<meta property="og:locale" content="en_US" />

{
  publishedTime && (
    <meta property="article:published_time" content={publishedTime} />
  )
}
{
  modifiedTime && (
    <meta property="article:modified_time" content={modifiedTime} />
  )
}
{tags.map((tag) => <meta property="article:tag" content={tag} />)}

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image" />
<meta property="twitter:url" content={url} />
<meta property="twitter:title" content={title} />
<meta property="twitter:description" content={description} />
<meta property="twitter:image" content={imageURL} />
<meta property="twitter:creator" content="@trinanda321" />

<!-- Additional SEO Meta Tags -->
<meta name="robots" content="index, follow" />
<meta name="googlebot" content="index, follow" />
<meta name="bingbot" content="index, follow" />
<meta name="revisit-after" content="7 days" />
<meta name="rating" content="general" />
<meta name="distribution" content="global" />

<!-- Keywords -->
{tags.length > 0 && <meta name="keywords" content={tags.join(", ")} />}

<!-- Structured Data -->
<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />

<!-- Additional Structured Data for Website -->
{
  schema === "WebSite" && (
    <script
      type="application/ld+json"
      set:html={JSON.stringify({
        "@context": "https://schema.org",
        "@type": "WebSite",
        name: "Muhammad Trinanda Portfolio",
        url: Astro.site?.href,
        description:
          "Portfolio website of Muhammad Trinanda - Data Analyst, Web Developer, and Graphic Designer",
        author: {
          "@type": "Person",
          name: "Muhammad Trinanda",
        },
        potentialAction: {
          "@type": "SearchAction",
          target: {
            "@type": "EntryPoint",
            urlTemplate: `${Astro.site?.href}search?q={search_term_string}`,
          },
          "query-input": "required name=search_term_string",
        },
      })}
    />
  )
}

<!-- Breadcrumb Structured Data -->
{
  Astro.url.pathname !== "/" && (
    <script
      type="application/ld+json"
      set:html={JSON.stringify({
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        itemListElement: [
          {
            "@type": "ListItem",
            position: 1,
            name: "Home",
            item: Astro.site?.href,
          },
          {
            "@type": "ListItem",
            position: 2,
            name: title,
            item: url,
          },
        ],
      })}
    />
  )
}

<!-- Preconnect to external domains -->
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link rel="preconnect" href="https://www.google-analytics.com" />
<link rel="preconnect" href="https://www.googletagmanager.com" />

<!-- DNS Prefetch -->
<link rel="dns-prefetch" href="https://fonts.googleapis.com" />
<link rel="dns-prefetch" href="https://fonts.gstatic.com" />
<link rel="dns-prefetch" href="https://www.google-analytics.com" />

<!-- Preload critical resources -->
<link
  rel="preload"
  href="/fonts/orbitron-v29-latin-regular.woff2"
  as="font"
  type="font/woff2"
  crossorigin
/>
<link rel="preload" href="/My Profile.png" as="image" />

<!-- Favicon and Icons -->
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="icon" type="image/png" href="/favicon.png" />
<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
<link rel="manifest" href="/site.webmanifest" />

<!-- Theme and Viewport -->
<meta name="theme-color" content="#00ffff" />
<meta name="color-scheme" content="dark" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />

<!-- Security Headers (X-Frame-Options moved to HTTP headers in vercel.json) -->
<meta http-equiv="X-Content-Type-Options" content="nosniff" />
<meta http-equiv="X-XSS-Protection" content="1; mode=block" />
<meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />

<!-- Performance Hints -->
<meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width" />

<style>
  /* Critical CSS for preventing layout shift */
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Orbitron", "Courier New", monospace;
    background-color: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
  }

  /* Prevent FOUC */
  .loading {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .loaded {
    opacity: 1;
  }
</style>
