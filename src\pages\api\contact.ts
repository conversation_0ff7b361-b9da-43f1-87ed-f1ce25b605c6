import type { APIRoute } from 'astro';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Rate limiting (simple in-memory store)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const RATE_LIMIT_MAX_REQUESTS = 5;

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  privacy: string;
}

// Validate form data
function validateFormData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.name || typeof data.name !== 'string' || data.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters long');
  }

  if (!data.email || typeof data.email !== 'string' || !EMAIL_REGEX.test(data.email)) {
    errors.push('Please provide a valid email address');
  }

  if (!data.subject || typeof data.subject !== 'string' || data.subject.trim().length === 0) {
    errors.push('Please select a subject');
  }

  if (!data.message || typeof data.message !== 'string' || data.message.trim().length < 10) {
    errors.push('Message must be at least 10 characters long');
  }

  if (data.message && data.message.length > 1000) {
    errors.push('Message must be less than 1000 characters');
  }

  if (!data.privacy || data.privacy !== 'on') {
    errors.push('You must agree to the privacy policy');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Check rate limiting
function checkRateLimit(clientIP: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const clientData = rateLimitStore.get(clientIP);

  if (!clientData || now > clientData.resetTime) {
    // Reset or create new entry
    rateLimitStore.set(clientIP, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW
    });
    return { allowed: true };
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, resetTime: clientData.resetTime };
  }

  // Increment count
  clientData.count++;
  rateLimitStore.set(clientIP, clientData);
  return { allowed: true };
}

// Sanitize input to prevent XSS
function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// Send email (mock implementation - replace with actual email service)
async function sendEmail(data: ContactFormData): Promise<boolean> {
  try {
    // Mock email sending - replace with actual service like SendGrid, Nodemailer, etc.
    console.log('Sending email:', {
      to: '<EMAIL>',
      from: data.email,
      subject: `Portfolio Contact: ${data.subject}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${data.name}</p>
        <p><strong>Email:</strong> ${data.email}</p>
        <p><strong>Subject:</strong> ${data.subject}</p>
        <p><strong>Message:</strong></p>
        <p>${data.message.replace(/\n/g, '<br>')}</p>
        <hr>
        <p><small>Sent from portfolio contact form</small></p>
      `
    });

    // Simulate async email sending
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In production, replace this with actual email service
    // Example with SendGrid:
    // const sgMail = require('@sendgrid/mail');
    // sgMail.setApiKey(process.env.SENDGRID_API_KEY);
    // await sgMail.send(emailData);
    
    return true;
  } catch (error) {
    console.error('Email sending failed:', error);
    return false;
  }
}

// Log form submission for analytics
function logSubmission(data: ContactFormData, clientIP: string) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    clientIP: clientIP.replace(/\d+$/, 'xxx'), // Anonymize IP
    subject: data.subject,
    messageLength: data.message.length,
    userAgent: 'contact-form'
  };
  
  console.log('Contact form submission:', logEntry);
  
  // In production, you might want to store this in a database
  // or send to analytics service
}

export const POST: APIRoute = async ({ request }) => {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';

    // Check rate limiting
    const rateLimitCheck = checkRateLimit(clientIP);
    if (!rateLimitCheck.allowed) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Too many requests. Please try again later.',
          resetTime: rateLimitCheck.resetTime
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': Math.ceil((rateLimitCheck.resetTime! - Date.now()) / 1000).toString()
          }
        }
      );
    }

    // Parse form data
    const contentType = request.headers.get('content-type');
    let formData: any;

    if (contentType?.includes('application/json')) {
      formData = await request.json();
    } else if (contentType?.includes('application/x-www-form-urlencoded')) {
      const form = await request.formData();
      formData = Object.fromEntries(form.entries());
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid content type'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Validate form data
    const validation = validateFormData(formData);
    if (!validation.isValid) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Validation failed',
          errors: validation.errors
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Sanitize input data
    const sanitizedData: ContactFormData = {
      name: sanitizeInput(formData.name),
      email: sanitizeInput(formData.email),
      subject: sanitizeInput(formData.subject),
      message: sanitizeInput(formData.message),
      privacy: formData.privacy
    };

    // Send email
    const emailSent = await sendEmail(sanitizedData);
    
    if (!emailSent) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to send email. Please try again later.'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Log submission for analytics
    logSubmission(sanitizedData, clientIP);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Message sent successfully! I\'ll get back to you soon.'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error. Please try again later.'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

// Handle OPTIONS request for CORS
export const OPTIONS: APIRoute = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  });
};
