---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import SkipLink from "@/components/SkipLink.astro";
import DynamicProjects from "@/components/DynamicProjects.astro";
import ThreeBackground from "@/components/ThreeBackground.astro";
import ThemeCustomizer from "@/components/ThemeCustomizer.astro";
---

<Layout
  title="Projects - Muhammad Trinanda"
  description="Explore my portfolio of web development, data analysis, and design projects. Built with modern technologies and creative solutions."
>
  <SkipLink />
  <Header />
  <ThreeBackground
    particleCount={800}
    enableInteraction={true}
    colorScheme="cyberpunk"
  />
  <ThemeCustomizer />

  <main id="main-content" class="pt-20">
    <!-- Hero Section -->
    <section class="py-20 relative">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h1
            class="text-5xl md:text-7xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-pink-400 to-purple-400 mb-6 glitch"
          >
            &lt;PROJECTS/&gt;
          </h1>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            A collection of my work spanning web development, data analysis, and creative design. 
            Each project represents a unique challenge solved with modern technologies and innovative approaches.
          </p>
        </div>

        <!-- Project Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          <div class="text-center cyber-card">
            <div class="text-3xl font-bold text-cyan-400 mb-2">15+</div>
            <div class="text-gray-400 text-sm">Projects Completed</div>
          </div>
          <div class="text-center cyber-card">
            <div class="text-3xl font-bold text-pink-400 mb-2">8</div>
            <div class="text-gray-400 text-sm">Technologies Used</div>
          </div>
          <div class="text-center cyber-card">
            <div class="text-3xl font-bold text-green-400 mb-2">3</div>
            <div class="text-gray-400 text-sm">Years Experience</div>
          </div>
          <div class="text-center cyber-card">
            <div class="text-3xl font-bold text-purple-400 mb-2">100%</div>
            <div class="text-gray-400 text-sm">Client Satisfaction</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Projects Section -->
    <section class="py-20 relative">
      <div class="container mx-auto px-6">
        <DynamicProjects showAll={true} />
      </div>
    </section>

    <!-- Technologies Section -->
    <section class="py-20 relative bg-gradient-to-t from-gray-900/50 to-transparent">
      <div class="container mx-auto px-6">
        <div class="text-center mb-16">
          <h2
            class="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-green-400 mb-4"
          >
            &lt;TECH_STACK/&gt;
          </h2>
          <p class="text-gray-400 font-mono">
            Technologies I use to bring ideas to life
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
          {[
            { name: "Astro", color: "from-orange-400 to-red-500" },
            { name: "React", color: "from-blue-400 to-cyan-500" },
            { name: "TypeScript", color: "from-blue-500 to-indigo-600" },
            { name: "Three.js", color: "from-green-400 to-emerald-500" },
            { name: "TailwindCSS", color: "from-cyan-400 to-blue-500" },
            { name: "GSAP", color: "from-pink-400 to-purple-500" },
            { name: "Node.js", color: "from-green-500 to-lime-600" },
            { name: "Python", color: "from-yellow-400 to-orange-500" },
            { name: "PostgreSQL", color: "from-blue-600 to-indigo-700" },
            { name: "Figma", color: "from-purple-400 to-pink-500" },
            { name: "Git", color: "from-orange-500 to-red-600" },
            { name: "Vercel", color: "from-gray-400 to-gray-600" }
          ].map((tech) => (
            <div class="tech-card group">
              <div class={`tech-icon bg-gradient-to-br ${tech.color}`}>
                <span class="tech-name">{tech.name.charAt(0)}</span>
              </div>
              <div class="tech-label">{tech.name}</div>
            </div>
          ))}
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 relative">
      <div class="container mx-auto px-6 text-center">
        <div class="cyber-card max-w-4xl mx-auto">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Start Your Next Project?
          </h2>
          <p class="text-gray-300 text-lg mb-8 max-w-2xl mx-auto">
            Let's collaborate and create something amazing together. 
            I'm always excited to work on new challenges and innovative solutions.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/#contact" class="cyber-button primary">
              <span class="relative z-10">GET IN TOUCH</span>
            </a>
            <a href="/blog" class="cyber-button secondary">
              <span class="relative z-10">READ MY BLOG</span>
            </a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <Footer />
</Layout>

<style>
  .tech-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .tech-card:hover {
    transform: translateY(-4px);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  .tech-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .tech-card:hover .tech-icon {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }

  .tech-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #000;
  }

  .tech-label {
    color: #e5e7eb;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
  }

  .cyber-card {
    @apply bg-black/40 border border-white/10 rounded-xl p-6 backdrop-blur-sm;
    transition: all 0.3s ease;
  }

  .cyber-card:hover {
    @apply border-cyan-400/30;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.1);
  }

  .cyber-button {
    @apply px-8 py-4 font-mono font-bold uppercase tracking-wider transition-all duration-300 relative overflow-hidden rounded-lg;
    background: linear-gradient(
      45deg,
      transparent,
      rgba(0, 255, 255, 0.1),
      transparent
    );
    border: 2px solid;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 100%, 20px 100%);
  }

  .cyber-button.primary {
    @apply border-cyan-400 text-cyan-400;
  }

  .cyber-button.primary:hover {
    @apply bg-cyan-400 text-black;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
  }

  .cyber-button.secondary {
    @apply border-pink-400 text-pink-400;
  }

  .cyber-button.secondary:hover {
    @apply bg-pink-400 text-black;
    box-shadow: 0 0 30px rgba(255, 0, 255, 0.5);
  }

  .glitch {
    position: relative;
    animation: glitch 2s infinite;
  }

  @keyframes glitch {
    0%, 100% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
  }

  @media (max-width: 768px) {
    .grid.grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-6 {
      grid-template-columns: repeat(3, 1fr);
    }
  }
</style>

<script>
  // Initialize GSAP animations for projects page
  import('/src/scripts/gsap-animations.js').then(({ default: AnimationController }) => {
    new AnimationController();
  });
</script>
