# Portfolio Environment Variables Template
# Copy this file to .env and fill in your actual values

# Site Configuration
PUBLIC_SITE_URL=https://trinanda-portfolio.vercel.app
PUBLIC_SITE_NAME="Muhammad Trinanda Portfolio"
PUBLIC_SITE_DESCRIPTION="Portfolio interaktif Muhammad Trinanda - Cyberpunk Edition"

# Analytics
PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
PUBLIC_HOTJAR_ID=0000000

# Contact Form (if using external service)
CONTACT_FORM_ENDPOINT=https://formspree.io/f/your-form-id
CONTACT_FORM_API_KEY=your-api-key

# Social Media
PUBLIC_GITHUB_URL=https://github.com/yourusername
PUBLIC_LINKEDIN_URL=https://linkedin.com/in/yourusername
PUBLIC_TWITTER_URL=https://twitter.com/yourusername
PUBLIC_INSTAGRAM_URL=https://instagram.com/yourusername

# Email Configuration (if using SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Database (if needed for future features)
DATABASE_URL=your-database-connection-string

# API Keys (for future integrations)
OPENAI_API_KEY=your-openai-key
GITHUB_TOKEN=your-github-token

# Security
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# Development
NODE_ENV=development
ASTRO_TELEMETRY_DISABLED=1

# Vercel Deployment (for CI/CD)
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id

# Performance Monitoring
SENTRY_DSN=your-sentry-dsn
PERFORMANCE_MONITORING_ENABLED=true

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CONTACT_FORM=true
ENABLE_BLOG=true
ENABLE_DARK_MODE=true
ENABLE_ANIMATIONS=true

# Content Management
CMS_API_URL=your-cms-api-url
CMS_API_KEY=your-cms-api-key

# Image Optimization
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Search (if implementing search functionality)
ALGOLIA_APP_ID=your-app-id
ALGOLIA_API_KEY=your-api-key
ALGOLIA_INDEX_NAME=portfolio-search
