---
// Advanced Analytics Dashboard Component
---

<div id="analytics-dashboard" class="fixed bottom-4 right-4 z-50 opacity-0 pointer-events-none transition-all duration-300">
  <div class="bg-gray-900/95 backdrop-blur-sm border border-cyan-500/30 rounded-lg p-4 min-w-[300px] shadow-2xl">
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-cyan-400 font-mono text-sm font-semibold">Analytics Dashboard</h3>
      <button 
        id="close-analytics" 
        class="text-gray-400 hover:text-cyan-400 transition-colors"
        aria-label="Close analytics dashboard"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    
    <div class="space-y-3">
      <!-- Performance Metrics -->
      <div class="bg-gray-800/50 rounded p-3">
        <h4 class="text-xs font-mono text-gray-300 mb-2">Performance</h4>
        <div class="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span class="text-gray-400">LCP:</span>
            <span id="lcp-value" class="text-green-400 ml-1">--</span>
          </div>
          <div>
            <span class="text-gray-400">FID:</span>
            <span id="fid-value" class="text-green-400 ml-1">--</span>
          </div>
          <div>
            <span class="text-gray-400">CLS:</span>
            <span id="cls-value" class="text-green-400 ml-1">--</span>
          </div>
          <div>
            <span class="text-gray-400">FCP:</span>
            <span id="fcp-value" class="text-green-400 ml-1">--</span>
          </div>
        </div>
      </div>

      <!-- User Interaction -->
      <div class="bg-gray-800/50 rounded p-3">
        <h4 class="text-xs font-mono text-gray-300 mb-2">User Activity</h4>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span class="text-gray-400">Page Views:</span>
            <span id="page-views" class="text-cyan-400">1</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">Time on Page:</span>
            <span id="time-on-page" class="text-cyan-400">0s</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">Scroll Depth:</span>
            <span id="scroll-depth" class="text-cyan-400">0%</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">Interactions:</span>
            <span id="interaction-count" class="text-cyan-400">0</span>
          </div>
        </div>
      </div>

      <!-- System Info -->
      <div class="bg-gray-800/50 rounded p-3">
        <h4 class="text-xs font-mono text-gray-300 mb-2">System</h4>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span class="text-gray-400">Browser:</span>
            <span id="browser-info" class="text-purple-400">--</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">Screen:</span>
            <span id="screen-info" class="text-purple-400">--</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">Connection:</span>
            <span id="connection-info" class="text-purple-400">--</span>
          </div>
        </div>
      </div>

      <!-- Theme Analytics -->
      <div class="bg-gray-800/50 rounded p-3">
        <h4 class="text-xs font-mono text-gray-300 mb-2">Theme Usage</h4>
        <div class="space-y-1 text-xs">
          <div class="flex justify-between">
            <span class="text-gray-400">Current:</span>
            <span id="current-theme" class="text-pink-400">cyberpunk</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-400">Changes:</span>
            <span id="theme-changes" class="text-pink-400">0</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Toggle Button -->
    <div class="mt-3 pt-3 border-t border-gray-700">
      <button 
        id="toggle-real-time" 
        class="w-full text-xs font-mono bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 py-1 px-2 rounded transition-colors"
      >
        Real-time: ON
      </button>
    </div>
  </div>
</div>

<!-- Analytics Toggle Button -->
<button 
  id="analytics-toggle" 
  class="fixed bottom-4 left-4 z-50 bg-gray-900/95 backdrop-blur-sm border border-cyan-500/30 rounded-lg p-3 text-cyan-400 hover:bg-cyan-500/10 transition-all duration-300 group"
  aria-label="Toggle analytics dashboard"
  title="Analytics Dashboard"
>
  <svg class="w-5 h-5 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
  </svg>
</button>

<script>
  class AnalyticsDashboard {
    constructor() {
      this.isVisible = false;
      this.isRealTime = true;
      this.startTime = Date.now();
      this.pageViews = 1;
      this.interactionCount = 0;
      this.themeChanges = 0;
      this.maxScrollDepth = 0;
      
      this.init();
    }

    init() {
      this.setupEventListeners();
      this.collectSystemInfo();
      this.startRealTimeUpdates();
      this.trackPerformanceMetrics();
      this.trackUserInteractions();
      this.trackScrollDepth();
    }

    setupEventListeners() {
      const toggleBtn = document.getElementById('analytics-toggle');
      const closeBtn = document.getElementById('close-analytics');
      const realTimeBtn = document.getElementById('toggle-real-time');
      const dashboard = document.getElementById('analytics-dashboard');

      toggleBtn?.addEventListener('click', () => this.toggleDashboard());
      closeBtn?.addEventListener('click', () => this.hideDashboard());
      realTimeBtn?.addEventListener('click', () => this.toggleRealTime());

      // Track theme changes
      if (window.themeSystem) {
        window.themeSystem.addObserver((themeName) => {
          this.themeChanges++;
          this.updateDisplay('theme-changes', this.themeChanges);
          this.updateDisplay('current-theme', themeName);
        });
      }
    }

    toggleDashboard() {
      const dashboard = document.getElementById('analytics-dashboard');
      if (this.isVisible) {
        this.hideDashboard();
      } else {
        this.showDashboard();
      }
    }

    showDashboard() {
      const dashboard = document.getElementById('analytics-dashboard');
      dashboard.style.opacity = '1';
      dashboard.style.pointerEvents = 'auto';
      dashboard.style.transform = 'translateY(0)';
      this.isVisible = true;
    }

    hideDashboard() {
      const dashboard = document.getElementById('analytics-dashboard');
      dashboard.style.opacity = '0';
      dashboard.style.pointerEvents = 'none';
      dashboard.style.transform = 'translateY(10px)';
      this.isVisible = false;
    }

    toggleRealTime() {
      this.isRealTime = !this.isRealTime;
      const btn = document.getElementById('toggle-real-time');
      btn.textContent = `Real-time: ${this.isRealTime ? 'ON' : 'OFF'}`;
      btn.className = this.isRealTime 
        ? 'w-full text-xs font-mono bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 py-1 px-2 rounded transition-colors'
        : 'w-full text-xs font-mono bg-gray-600/20 hover:bg-gray-600/30 text-gray-400 py-1 px-2 rounded transition-colors';
    }

    collectSystemInfo() {
      // Browser info
      const browserInfo = this.getBrowserInfo();
      this.updateDisplay('browser-info', browserInfo);

      // Screen info
      const screenInfo = `${screen.width}x${screen.height}`;
      this.updateDisplay('screen-info', screenInfo);

      // Connection info
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      const connectionInfo = connection ? connection.effectiveType || 'unknown' : 'unknown';
      this.updateDisplay('connection-info', connectionInfo);
    }

    getBrowserInfo() {
      const ua = navigator.userAgent;
      if (ua.includes('Chrome')) return 'Chrome';
      if (ua.includes('Firefox')) return 'Firefox';
      if (ua.includes('Safari')) return 'Safari';
      if (ua.includes('Edge')) return 'Edge';
      return 'Unknown';
    }

    trackPerformanceMetrics() {
      // Web Vitals tracking
      if ('PerformanceObserver' in window) {
        // LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.updateDisplay('lcp-value', `${Math.round(lastEntry.startTime)}ms`);
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

        // FID
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const fid = entry.processingStart - entry.startTime;
            this.updateDisplay('fid-value', `${Math.round(fid)}ms`);
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });

        // CLS
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          this.updateDisplay('cls-value', clsValue.toFixed(3));
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });

        // FCP
        const fcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.updateDisplay('fcp-value', `${Math.round(entry.startTime)}ms`);
            }
          }
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
      }
    }

    trackUserInteractions() {
      ['click', 'keydown', 'scroll', 'mousemove'].forEach(event => {
        document.addEventListener(event, () => {
          this.interactionCount++;
          this.updateDisplay('interaction-count', this.interactionCount);
        }, { passive: true });
      });
    }

    trackScrollDepth() {
      window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = Math.round((scrollTop / docHeight) * 100);
        
        if (scrollPercent > this.maxScrollDepth) {
          this.maxScrollDepth = scrollPercent;
          this.updateDisplay('scroll-depth', `${this.maxScrollDepth}%`);
        }
      }, { passive: true });
    }

    startRealTimeUpdates() {
      setInterval(() => {
        if (this.isRealTime) {
          const timeOnPage = Math.floor((Date.now() - this.startTime) / 1000);
          this.updateDisplay('time-on-page', `${timeOnPage}s`);
        }
      }, 1000);
    }

    updateDisplay(elementId, value) {
      const element = document.getElementById(elementId);
      if (element) {
        element.textContent = value;
      }
    }
  }

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.analyticsDashboard = new AnalyticsDashboard();
  });
</script>

<style>
  #analytics-dashboard {
    transform: translateY(10px);
  }
  
  #analytics-toggle:hover {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }
</style>
