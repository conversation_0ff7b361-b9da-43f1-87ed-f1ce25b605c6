---
export interface Props {
  class?: string;
  glowColor?: string;
  hoverScale?: number;
  rotateOnHover?: boolean;
}

const {
  class: className = "",
  glowColor = "cyan",
  hoverScale = 1.05,
  rotateOnHover = false,
  ...rest
} = Astro.props;

const glowColorMap = {
  cyan: "rgba(0, 255, 255, 0.3)",
  pink: "rgba(255, 0, 255, 0.3)",
  green: "rgba(0, 255, 0, 0.3)",
  purple: "rgba(138, 43, 226, 0.3)",
  orange: "rgba(255, 102, 0, 0.3)",
};

const selectedGlow = glowColorMap[glowColor] || glowColorMap.cyan;
---

<div
  class={`interactive-card ${className}`}
  data-glow-color={selectedGlow}
  data-hover-scale={hoverScale}
  data-rotate-hover={rotateOnHover}
  {...rest}
>
  <div class="card-content">
    <slot />
  </div>
  <div class="card-glow"></div>
  <div class="card-border"></div>
</div>

<style define:vars={{ glowColor: selectedGlow }}>
  .interactive-card {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
    cursor: pointer;
  }

  .card-content {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
  }

  .card-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--glowColor), transparent, var(--glowColor));
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
    filter: blur(8px);
  }

  .card-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid transparent;
    border-radius: inherit;
    background: linear-gradient(45deg, var(--glowColor), transparent) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: exclude;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }

  .interactive-card:hover .card-glow {
    opacity: 0.6;
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .interactive-card:hover .card-border {
    opacity: 1;
  }

  .interactive-card:hover .card-content {
    transform: translateY(-2px);
  }

  @keyframes pulse-glow {
    0%, 100% {
      opacity: 0.6;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }

  /* 3D tilt effect */
  .interactive-card[data-rotate-hover="true"] {
    perspective: 1000px;
  }

  .interactive-card[data-rotate-hover="true"]:hover {
    transform: rotateX(5deg) rotateY(5deg);
  }

  /* Magnetic effect */
  .interactive-card.magnetic {
    transition: transform 0.1s ease-out;
  }

  /* Ripple effect */
  .interactive-card::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: var(--glowColor);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    z-index: 0;
    opacity: 0;
  }

  .interactive-card:active::after {
    width: 300px;
    height: 300px;
    opacity: 0.3;
    transition: width 0s, height 0s, opacity 0.3s;
  }
</style>

<script>
  class InteractiveCardController {
    constructor() {
      this.cards = document.querySelectorAll('.interactive-card');
      this.init();
    }

    init() {
      this.cards.forEach(card => {
        this.setupCard(card);
      });
    }

    setupCard(card) {
      const hoverScale = parseFloat(card.dataset.hoverScale) || 1.05;
      const rotateHover = card.dataset.rotateHover === 'true';

      // Mouse enter
      card.addEventListener('mouseenter', (e) => {
        this.onMouseEnter(e, card, hoverScale);
      });

      // Mouse leave
      card.addEventListener('mouseleave', (e) => {
        this.onMouseLeave(e, card);
      });

      // Mouse move for tilt effect
      if (rotateHover) {
        card.addEventListener('mousemove', (e) => {
          this.onMouseMove(e, card);
        });
      }

      // Click ripple effect
      card.addEventListener('click', (e) => {
        this.createRipple(e, card);
      });
    }

    onMouseEnter(e, card, scale) {
      card.style.transform = `scale(${scale})`;
      card.style.zIndex = '10';
    }

    onMouseLeave(e, card) {
      card.style.transform = 'scale(1) rotateX(0deg) rotateY(0deg)';
      card.style.zIndex = 'auto';
    }

    onMouseMove(e, card) {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      const rotateX = (y - centerY) / centerY * -10;
      const rotateY = (x - centerX) / centerX * 10;
      
      const hoverScale = parseFloat(card.dataset.hoverScale) || 1.05;
      
      card.style.transform = `scale(${hoverScale}) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    }

    createRipple(e, card) {
      const rect = card.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const ripple = document.createElement('div');
      ripple.style.position = 'absolute';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.style.width = '0';
      ripple.style.height = '0';
      ripple.style.borderRadius = '50%';
      ripple.style.background = card.dataset.glowColor || 'rgba(0, 255, 255, 0.3)';
      ripple.style.transform = 'translate(-50%, -50%)';
      ripple.style.pointerEvents = 'none';
      ripple.style.zIndex = '1';
      
      card.appendChild(ripple);
      
      // Animate ripple
      ripple.animate([
        { width: '0', height: '0', opacity: 0.8 },
        { width: '200px', height: '200px', opacity: 0 }
      ], {
        duration: 600,
        easing: 'ease-out'
      }).onfinish = () => {
        ripple.remove();
      };
    }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new InteractiveCardController();
  });
</script>
