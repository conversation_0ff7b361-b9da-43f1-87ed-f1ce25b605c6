---
import type { BaseProps } from '@/env.d.ts';

export interface Props extends BaseProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'cyan' | 'pink' | 'white';
  text?: string;
}

const { 
  class: className,
  size = 'md',
  color = 'cyan',
  text = 'Loading...'
} = Astro.props;

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-8 h-8', 
  lg: 'w-12 h-12'
};

const colorClasses = {
  cyan: 'border-cyan-400',
  pink: 'border-pink-400',
  white: 'border-white'
};
---

<div 
  class={`inline-flex items-center gap-3 ${className || ''}`}
  role="status"
  aria-live="polite"
  aria-label={text}
>
  <div 
    class={`${sizeClasses[size]} ${colorClasses[color]} border-2 border-t-transparent rounded-full animate-spin`}
    aria-hidden="true"
  ></div>
  <span class="text-sm font-medium text-gray-300">
    {text}
  </span>
</div>

<style>
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
</style>
