import { defineCollection, z } from "astro:content";
import { glob, file } from "astro/loaders";

const blog = defineCollection({
  loader: glob({ pattern: "**/*.{md,mdx}", base: "./src/content/blog" }),
  schema: z.object({
    title: z.string(),
    description: z.string(),
    publishedAt: z.coerce.date(),
    updatedAt: z.coerce.date().optional(),
    image: z.string().optional(),
    imageAlt: z.string().optional(),
    tags: z.array(z.string()).default([]),
    category: z.string(),
    featured: z.boolean().default(false),
    draft: z.boolean().default(false),
    author: z.string().default("Muhammad Trinanda"),
    readingTime: z.number().default(5),
    seo: z
      .object({
        title: z.string().optional(),
        description: z.string().optional(),
        keywords: z.array(z.string()).default([]),
      })
      .optional(),
  }),
});

const projects = defineCollection({
  loader: glob({ pattern: "**/*.{md,mdx}", base: "./src/content/projects" }),
  schema: z.object({
    title: z.string(),
    description: z.string(),
    image: z.string(),
    technologies: z.array(z.string()),
    githubUrl: z.string().url().optional(),
    liveUrl: z.string().url().optional(),
    featured: z.boolean().default(false),
    status: z
      .enum(["completed", "in-progress", "planned"])
      .default("completed"),
    startDate: z.coerce.date(),
    endDate: z.coerce.date().optional(),
    category: z.enum(["web", "mobile", "desktop", "data-analysis", "design"]),
    difficulty: z
      .enum(["beginner", "intermediate", "advanced"])
      .default("intermediate"),
    teamSize: z.number().min(1).default(1),
    highlights: z.array(z.string()).default([]),
    challenges: z.array(z.string()).default([]),
    learnings: z.array(z.string()).default([]),
  }),
});

const skills = defineCollection({
  loader: file("src/content/skills/skills.json"),
  schema: z.object({
    id: z.string(),
    name: z.string(),
    category: z.enum([
      "frontend",
      "backend",
      "database",
      "tools",
      "design",
      "finance",
      "soft-skills",
    ]),
    level: z.number().min(1).max(100),
    icon: z.string().optional(),
    description: z.string().optional(),
    experience: z.string().optional(),
    certifications: z.array(z.string()).default([]),
    projects: z.array(z.string()).default([]),
    lastUsed: z.coerce.date().optional(),
  }),
});

export const collections = { blog, projects, skills };
