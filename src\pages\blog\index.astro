---
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import AdvancedSearch from "@/components/AdvancedSearch.astro";
import { getCollection } from "astro:content";

// Get all blog posts
const allPosts = await getCollection("blog");
const publishedPosts = allPosts
  .filter((post) => !post.data.draft)
  .sort(
    (a, b) =>
      new Date(b.data.publishedAt).getTime() -
      new Date(a.data.publishedAt).getTime()
  );

// Get featured posts
const featuredPosts = publishedPosts.filter((post) => post.data.featured);

// Get categories and tags
const allCategories = [
  ...new Set(publishedPosts.map((post) => post.data.category)),
];
const allTags = [
  ...new Set(publishedPosts.flatMap((post) => post.data.tags || [])),
];
---

<Layout
  title="Blog - <PERSON>"
  description="Thoughts, tutorials, and insights about web development, technology, and design"
>
  <Header />

  <main class="min-h-screen pt-20 pb-16">
    <div class="container mx-auto px-6">
      <!-- Blog Header -->
      <section class="text-center py-16">
        <h1
          class="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 bg-clip-text text-transparent"
        >
          Tech Blog
        </h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
          Exploring the intersection of technology, creativity, and innovation
        </p>
        <div class="flex flex-wrap justify-center gap-4 text-sm font-mono">
          <span class="bg-cyan-500/20 text-cyan-400 px-3 py-1 rounded">
            {publishedPosts.length} Articles
          </span>
          <span class="bg-purple-500/20 text-purple-400 px-3 py-1 rounded">
            {allCategories.length} Categories
          </span>
          <span class="bg-pink-500/20 text-pink-400 px-3 py-1 rounded">
            {allTags.length} Tags
          </span>
        </div>
      </section>

      <!-- Advanced Search -->
      <AdvancedSearch />

      <!-- Featured Posts -->
      {
        featuredPosts.length > 0 && (
          <section class="mb-16">
            <h2 class="text-2xl font-bold text-cyan-400 mb-8 font-mono">
              Featured Articles
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredPosts.slice(0, 3).map((post) => (
                <article class="group bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-cyan-500/50 transition-all duration-300">
                  {post.data.image && (
                    <div class="aspect-video overflow-hidden">
                      <img
                        src={post.data.image}
                        alt={post.data.title}
                        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}
                  <div class="p-6">
                    <div class="flex items-center gap-2 mb-3">
                      <span class="bg-cyan-500/20 text-cyan-400 px-2 py-1 rounded text-xs font-mono">
                        {post.data.category}
                      </span>
                      <span class="text-gray-500 text-xs">
                        {new Date(post.data.publishedAt).toLocaleDateString()}
                      </span>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3 group-hover:text-cyan-400 transition-colors">
                      <a href={`/blog/${post.id}`} class="hover:underline">
                        {post.data.title}
                      </a>
                    </h3>
                    <p class="text-gray-300 text-sm mb-4 line-clamp-3">
                      {post.data.description}
                    </p>
                    <div class="flex flex-wrap gap-2 mb-4">
                      {post.data.tags?.slice(0, 3).map((tag) => (
                        <span class="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">
                          #{tag}
                        </span>
                      ))}
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-gray-500 text-xs">
                        {post.data.readingTime} min read
                      </span>
                      <a
                        href={`/blog/${post.id}`}
                        class="text-cyan-400 hover:text-cyan-300 text-sm font-mono transition-colors"
                      >
                        Read more →
                      </a>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </section>
        )
      }

      <!-- All Posts -->
      <section>
        <div class="flex items-center justify-between mb-8">
          <h2 class="text-2xl font-bold text-cyan-400 font-mono">
            All Articles
          </h2>
          <div class="flex items-center gap-4">
            <!-- View Toggle -->
            <div class="flex bg-gray-800 rounded-lg p-1">
              <button id="grid-view" class="view-btn active" data-view="grid">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                  ></path>
                </svg>
              </button>
              <button id="list-view" class="view-btn" data-view="list">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>

            <!-- Sort Options -->
            <select
              id="sort-posts"
              class="bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm"
            >
              <option value="date-desc">Latest First</option>
              <option value="date-asc">Oldest First</option>
              <option value="title-asc">Title A-Z</option>
              <option value="title-desc">Title Z-A</option>
              <option value="reading-time">Reading Time</option>
            </select>
          </div>
        </div>

        <!-- Posts Grid -->
        <div
          id="posts-container"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {
            publishedPosts.map((post) => (
              <article
                class="post-card group bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-cyan-500/50 transition-all duration-300"
                data-category={post.data.category}
                data-tags={post.data.tags?.join(",")}
                data-date={post.data.publishedAt}
                data-title={post.data.title}
                data-reading-time={post.data.readingTime}
              >
                {post.data.image && (
                  <div class="aspect-video overflow-hidden">
                    <img
                      src={post.data.image}
                      alt={post.data.title}
                      class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                  </div>
                )}
                <div class="p-6">
                  <div class="flex items-center gap-2 mb-3">
                    <span class="bg-cyan-500/20 text-cyan-400 px-2 py-1 rounded text-xs font-mono">
                      {post.data.category}
                    </span>
                    <span class="text-gray-500 text-xs">
                      {new Date(post.data.publishedAt).toLocaleDateString()}
                    </span>
                    {post.data.featured && (
                      <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs font-mono">
                        Featured
                      </span>
                    )}
                  </div>
                  <h3 class="text-xl font-semibold text-white mb-3 group-hover:text-cyan-400 transition-colors">
                    <a href={`/blog/${post.id}`} class="hover:underline">
                      {post.data.title}
                    </a>
                  </h3>
                  <p class="text-gray-300 text-sm mb-4 line-clamp-3">
                    {post.data.description}
                  </p>
                  <div class="flex flex-wrap gap-2 mb-4">
                    {post.data.tags?.slice(0, 3).map((tag) => (
                      <span class="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs hover:bg-gray-600 cursor-pointer transition-colors">
                        #{tag}
                      </span>
                    ))}
                    {post.data.tags?.length > 3 && (
                      <span class="text-gray-500 text-xs">
                        +{post.data.tags.length - 3} more
                      </span>
                    )}
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-gray-500 text-xs">
                      {post.data.readingTime} min read
                    </span>
                    <a
                      href={`/blog/${post.id}`}
                      class="text-cyan-400 hover:text-cyan-300 text-sm font-mono transition-colors"
                    >
                      Read more →
                    </a>
                  </div>
                </div>
              </article>
            ))
          }
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-12">
          <button
            id="load-more"
            class="bg-cyan-500/20 hover:bg-cyan-500/30 text-cyan-400 px-8 py-3 rounded-lg font-mono transition-colors"
          >
            Load More Articles
          </button>
        </div>
      </section>

      <!-- Categories & Tags Sidebar -->
      <aside class="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Categories -->
        <div
          class="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6"
        >
          <h3 class="text-lg font-bold text-cyan-400 mb-4 font-mono">
            Categories
          </h3>
          <div class="space-y-2">
            {
              allCategories.map((category) => {
                const count = publishedPosts.filter(
                  (post) => post.data.category === category
                ).length;
                return (
                  <button
                    class="category-filter w-full text-left flex items-center justify-between p-2 rounded hover:bg-gray-800 transition-colors"
                    data-category={category}
                  >
                    <span class="text-gray-300">{category}</span>
                    <span class="bg-gray-700 text-gray-400 px-2 py-1 rounded text-xs">
                      {count}
                    </span>
                  </button>
                );
              })
            }
          </div>
        </div>

        <!-- Popular Tags -->
        <div
          class="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6"
        >
          <h3 class="text-lg font-bold text-cyan-400 mb-4 font-mono">
            Popular Tags
          </h3>
          <div class="flex flex-wrap gap-2">
            {
              allTags.slice(0, 20).map((tag) => {
                const count = publishedPosts.filter((post) =>
                  post.data.tags?.includes(tag)
                ).length;
                return (
                  <button
                    class="tag-filter bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded text-sm transition-colors"
                    data-tag={tag}
                  >
                    #{tag} ({count})
                  </button>
                );
              })
            }
          </div>
        </div>
      </aside>
    </div>
  </main>

  <Footer />
</Layout>

<script>
  class BlogManager {
    constructor() {
      this.currentView = "grid";
      this.currentSort = "date-desc";
      this.visiblePosts = 9;
      this.postsPerLoad = 6;
      this.allPosts = Array.from(document.querySelectorAll(".post-card"));
      this.filteredPosts = [...this.allPosts];

      this.init();
    }

    init() {
      this.setupEventListeners();
      this.updateView();
      this.showPosts();
    }

    setupEventListeners() {
      // View toggle
      document.querySelectorAll(".view-btn").forEach((btn) => {
        btn.addEventListener("click", (e) =>
          this.switchView(e.target.dataset.view)
        );
      });

      // Sort
      document.getElementById("sort-posts")?.addEventListener("change", (e) => {
        this.sortPosts(e.target.value);
      });

      // Load more
      document.getElementById("load-more")?.addEventListener("click", () => {
        this.loadMorePosts();
      });

      // Category filters
      document.querySelectorAll(".category-filter").forEach((btn) => {
        btn.addEventListener("click", (e) =>
          this.filterByCategory(e.target.dataset.category)
        );
      });

      // Tag filters
      document.querySelectorAll(".tag-filter").forEach((btn) => {
        btn.addEventListener("click", (e) =>
          this.filterByTag(e.target.dataset.tag)
        );
      });

      // Search results
      document.addEventListener("searchResults", (e) => {
        this.handleSearchResults(e.detail);
      });
    }

    switchView(view) {
      this.currentView = view;

      // Update active button
      document.querySelectorAll(".view-btn").forEach((btn) => {
        btn.classList.remove("active");
      });
      document.querySelector(`[data-view="${view}"]`)?.classList.add("active");

      this.updateView();
    }

    updateView() {
      const container = document.getElementById("posts-container");

      if (this.currentView === "list") {
        container.className = "space-y-6";
        this.allPosts.forEach((post) => {
          post.className = post.className.replace(
            "group bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-cyan-500/50 transition-all duration-300",
            "group bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg overflow-hidden hover:border-cyan-500/50 transition-all duration-300 flex"
          );
        });
      } else {
        container.className =
          "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8";
        this.allPosts.forEach((post) => {
          post.className = post.className.replace("flex", "");
        });
      }
    }

    sortPosts(sortType) {
      this.currentSort = sortType;

      this.filteredPosts.sort((a, b) => {
        switch (sortType) {
          case "date-desc":
            return new Date(b.dataset.date) - new Date(a.dataset.date);
          case "date-asc":
            return new Date(a.dataset.date) - new Date(b.dataset.date);
          case "title-asc":
            return a.dataset.title.localeCompare(b.dataset.title);
          case "title-desc":
            return b.dataset.title.localeCompare(a.dataset.title);
          case "reading-time":
            return (
              parseInt(a.dataset.readingTime) - parseInt(b.dataset.readingTime)
            );
          default:
            return 0;
        }
      });

      this.visiblePosts = 9;
      this.showPosts();
    }

    filterByCategory(category) {
      this.filteredPosts = this.allPosts.filter(
        (post) => post.dataset.category === category
      );
      this.visiblePosts = 9;
      this.showPosts();
    }

    filterByTag(tag) {
      this.filteredPosts = this.allPosts.filter((post) =>
        post.dataset.tags?.includes(tag)
      );
      this.visiblePosts = 9;
      this.showPosts();
    }

    handleSearchResults(searchData) {
      // Filter posts based on search results
      if (searchData.query) {
        const query = searchData.query.toLowerCase();
        this.filteredPosts = this.allPosts.filter((post) => {
          const title = post.dataset.title.toLowerCase();
          const category = post.dataset.category.toLowerCase();
          const tags = post.dataset.tags?.toLowerCase() || "";

          return (
            title.includes(query) ||
            category.includes(query) ||
            tags.includes(query)
          );
        });
      } else {
        this.filteredPosts = [...this.allPosts];
      }

      this.visiblePosts = 9;
      this.showPosts();
    }

    showPosts() {
      // Hide all posts
      this.allPosts.forEach((post) => {
        post.style.display = "none";
      });

      // Show filtered posts up to visible limit
      this.filteredPosts.slice(0, this.visiblePosts).forEach((post) => {
        post.style.display = "block";
      });

      // Update load more button
      const loadMoreBtn = document.getElementById("load-more");
      if (this.visiblePosts >= this.filteredPosts.length) {
        loadMoreBtn.style.display = "none";
      } else {
        loadMoreBtn.style.display = "block";
      }
    }

    loadMorePosts() {
      this.visiblePosts += this.postsPerLoad;
      this.showPosts();
    }
  }

  // Initialize when DOM is ready
  document.addEventListener("DOMContentLoaded", () => {
    window.blogManager = new BlogManager();
  });
</script>

<style>
  .view-btn {
    @apply p-2 text-gray-400 hover:text-white transition-colors rounded;
  }

  .view-btn.active {
    @apply bg-cyan-500/20 text-cyan-400;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
