---
// Analytics component for tracking user behavior and performance
const isDev = import.meta.env.DEV;
const GA_TRACKING_ID = import.meta.env.PUBLIC_GA_TRACKING_ID || 'G-XXXXXXXXXX';
const HOTJAR_ID = import.meta.env.PUBLIC_HOTJAR_ID;
---

{!isDev && (
  <>
    <!-- Google Analytics 4 -->
    <script async src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}></script>
    <script define:vars={{ GA_TRACKING_ID }} is:inline>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', GA_TRACKING_ID, {
        page_title: document.title,
        page_location: window.location.href,
        send_page_view: true,
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false
      });

      // Enhanced ecommerce tracking for portfolio interactions
      gtag('config', GA_TRACKING_ID, {
        custom_map: {
          'custom_parameter_1': 'project_view',
          'custom_parameter_2': 'contact_form',
          'custom_parameter_3': 'theme_change'
        }
      });
    </script>

    <!-- Hotjar Tracking Code -->
    {HOTJAR_ID && (
      <script define:vars={{ HOTJAR_ID }} is:inline>
        (function(h,o,t,j,a,r){
          h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
          h._hjSettings={hjid:HOTJAR_ID,hjsv:6};
          a=o.getElementsByTagName('head')[0];
          r=o.createElement('script');r.async=1;
          r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
          a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
      </script>
    )}
  </>
)}

<!-- Custom Analytics Script -->
<script>
  class PortfolioAnalytics {
    constructor() {
      this.isDev = import.meta.env.DEV;
      this.sessionStart = Date.now();
      this.interactions = [];
      this.performanceMetrics = {};
      
      this.init();
    }

    init() {
      this.trackPageLoad();
      this.trackUserInteractions();
      this.trackPerformanceMetrics();
      this.trackScrollDepth();
      this.trackTimeOnPage();
      
      // Send data before page unload
      window.addEventListener('beforeunload', () => {
        this.sendAnalytics();
      });

      // Send data periodically
      setInterval(() => {
        this.sendAnalytics();
      }, 30000); // Every 30 seconds
    }

    trackPageLoad() {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      this.performanceMetrics.pageLoadTime = loadTime;
      
      if (!this.isDev && window.gtag) {
        gtag('event', 'page_load_time', {
          event_category: 'Performance',
          event_label: 'Page Load',
          value: Math.round(loadTime)
        });
      }
    }

    trackUserInteractions() {
      // Track button clicks
      document.addEventListener('click', (e) => {
        const target = e.target.closest('button, a, .interactive');
        if (target) {
          const interaction = {
            type: 'click',
            element: target.tagName.toLowerCase(),
            text: target.textContent?.trim().substring(0, 50),
            href: target.href || null,
            timestamp: Date.now(),
            x: e.clientX,
            y: e.clientY
          };
          
          this.interactions.push(interaction);
          
          if (!this.isDev && window.gtag) {
            gtag('event', 'click', {
              event_category: 'Engagement',
              event_label: interaction.text || interaction.element,
              custom_parameter_1: target.dataset.track || 'general'
            });
          }
        }
      });

      // Track form submissions
      document.addEventListener('submit', (e) => {
        const form = e.target;
        const formData = new FormData(form);
        const interaction = {
          type: 'form_submit',
          formId: form.id,
          fields: Array.from(formData.keys()),
          timestamp: Date.now()
        };
        
        this.interactions.push(interaction);
        
        if (!this.isDev && window.gtag) {
          gtag('event', 'form_submit', {
            event_category: 'Conversion',
            event_label: form.id || 'contact_form'
          });
        }
      });

      // Track theme changes
      document.addEventListener('themeChange', (e) => {
        const interaction = {
          type: 'theme_change',
          theme: e.detail.theme,
          timestamp: Date.now()
        };
        
        this.interactions.push(interaction);
        
        if (!this.isDev && window.gtag) {
          gtag('event', 'theme_change', {
            event_category: 'Customization',
            event_label: e.detail.theme,
            custom_parameter_3: 'theme_customizer'
          });
        }
      });
    }

    trackScrollDepth() {
      let maxScroll = 0;
      const milestones = [25, 50, 75, 90, 100];
      const reached = new Set();

      window.addEventListener('scroll', () => {
        const scrollPercent = Math.round(
          (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
        );
        
        maxScroll = Math.max(maxScroll, scrollPercent);
        
        milestones.forEach(milestone => {
          if (scrollPercent >= milestone && !reached.has(milestone)) {
            reached.add(milestone);
            
            if (!this.isDev && window.gtag) {
              gtag('event', 'scroll_depth', {
                event_category: 'Engagement',
                event_label: `${milestone}%`,
                value: milestone
              });
            }
          }
        });
      });
    }

    trackTimeOnPage() {
      // Track time spent on page
      setInterval(() => {
        const timeOnPage = Math.round((Date.now() - this.sessionStart) / 1000);
        
        // Send milestone events
        if (timeOnPage % 30 === 0 && timeOnPage > 0) { // Every 30 seconds
          if (!this.isDev && window.gtag) {
            gtag('event', 'time_on_page', {
              event_category: 'Engagement',
              event_label: 'Time Milestone',
              value: timeOnPage
            });
          }
        }
      }, 1000);
    }

    trackPerformanceMetrics() {
      // Core Web Vitals
      if ('web-vital' in window) {
        import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
          getCLS((metric) => {
            this.performanceMetrics.cls = metric.value;
            this.sendMetric('CLS', metric.value);
          });

          getFID((metric) => {
            this.performanceMetrics.fid = metric.value;
            this.sendMetric('FID', metric.value);
          });

          getFCP((metric) => {
            this.performanceMetrics.fcp = metric.value;
            this.sendMetric('FCP', metric.value);
          });

          getLCP((metric) => {
            this.performanceMetrics.lcp = metric.value;
            this.sendMetric('LCP', metric.value);
          });

          getTTFB((metric) => {
            this.performanceMetrics.ttfb = metric.value;
            this.sendMetric('TTFB', metric.value);
          });
        });
      }

      // Custom performance metrics
      if (performance.getEntriesByType) {
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach(entry => {
          this.performanceMetrics[entry.name] = entry.startTime;
        });
      }
    }

    sendMetric(name, value) {
      if (!this.isDev && window.gtag) {
        gtag('event', 'web_vital', {
          event_category: 'Performance',
          event_label: name,
          value: Math.round(value),
          non_interaction: true
        });
      }
    }

    sendAnalytics() {
      if (this.isDev) {
        console.log('Analytics Data:', {
          interactions: this.interactions,
          performance: this.performanceMetrics,
          timeOnPage: Date.now() - this.sessionStart
        });
        return;
      }

      // Send to custom analytics endpoint if needed
      if (this.interactions.length > 0) {
        fetch('/api/analytics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            interactions: this.interactions,
            performance: this.performanceMetrics,
            session: {
              start: this.sessionStart,
              duration: Date.now() - this.sessionStart,
              url: window.location.href,
              referrer: document.referrer,
              userAgent: navigator.userAgent
            }
          })
        }).catch(err => {
          console.warn('Analytics send failed:', err);
        });

        // Clear sent interactions
        this.interactions = [];
      }
    }

    // Public methods for manual tracking
    trackEvent(eventName, category, label, value) {
      const interaction = {
        type: 'custom_event',
        eventName,
        category,
        label,
        value,
        timestamp: Date.now()
      };
      
      this.interactions.push(interaction);
      
      if (!this.isDev && window.gtag) {
        gtag('event', eventName, {
          event_category: category,
          event_label: label,
          value: value
        });
      }
    }

    trackProjectView(projectName) {
      this.trackEvent('project_view', 'Portfolio', projectName);
    }

    trackContactFormStart() {
      this.trackEvent('contact_form_start', 'Conversion', 'Form Interaction');
    }

    trackThemeChange(themeName) {
      this.trackEvent('theme_change', 'Customization', themeName);
    }
  }

  // Initialize analytics when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    window.portfolioAnalytics = new PortfolioAnalytics();
  });

  // Export for global access
  window.trackEvent = (eventName, category, label, value) => {
    if (window.portfolioAnalytics) {
      window.portfolioAnalytics.trackEvent(eventName, category, label, value);
    }
  };
</script>

<!-- Performance Observer for additional metrics -->
<script>
  if ('PerformanceObserver' in window) {
    // Observe layout shifts
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0;
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      
      if (clsValue > 0 && window.gtag && !import.meta.env.DEV) {
        gtag('event', 'layout_shift', {
          event_category: 'Performance',
          event_label: 'CLS',
          value: Math.round(clsValue * 1000),
          non_interaction: true
        });
      }
    });

    clsObserver.observe({ entryTypes: ['layout-shift'] });

    // Observe long tasks
    const longTaskObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (window.gtag && !import.meta.env.DEV) {
          gtag('event', 'long_task', {
            event_category: 'Performance',
            event_label: 'Long Task',
            value: Math.round(entry.duration),
            non_interaction: true
          });
        }
      }
    });

    longTaskObserver.observe({ entryTypes: ['longtask'] });
  }
</script>
