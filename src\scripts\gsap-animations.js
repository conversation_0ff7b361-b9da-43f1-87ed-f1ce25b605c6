import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { TextPlugin } from "gsap/TextPlugin";
import { MorphSVGPlugin } from "gsap/MorphSVGPlugin";
import { MotionPathPlugin } from "gsap/MotionPathPlugin";
import { Observer } from "gsap/Observer";

// Register GSAP plugins
gsap.registerPlugin(
  ScrollTrigger,
  TextPlugin,
  MorphSVGPlugin,
  MotionPathPlugin,
  Observer
);

class AnimationController {
  constructor() {
    this.tl = gsap.timeline();
    this.init();
  }

  init() {
    this.setupScrollTriggers();
    this.setupHeroAnimations();
    this.setupTypingAnimation();
    this.setupSkillBars();
    this.setupProjectCards();
    this.setupGlitchEffects();
    this.setupAdvancedAnimations();
    this.setupMicroInteractions();
    this.setupParallaxEffects();
    this.setupMorphingElements();
  }

  setupHeroAnimations() {
    // Hero entrance animation
    const heroTl = gsap.timeline({ delay: 0.5 });

    heroTl
      .from(".hero-section .text-sm", {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: "power3.out",
      })
      .from(
        ".hero-section h1",
        {
          duration: 1.2,
          y: 100,
          opacity: 0,
          ease: "power3.out",
          stagger: 0.1,
        },
        "-=0.5"
      )
      .from(
        ".hero-section .text-xl",
        {
          duration: 1,
          y: 30,
          opacity: 0,
          ease: "power2.out",
        },
        "-=0.3"
      )
      .from(
        ".hero-section p",
        {
          duration: 1,
          y: 30,
          opacity: 0,
          ease: "power2.out",
        },
        "-=0.2"
      )
      .from(
        ".hero-section .cyber-button",
        {
          duration: 0.8,
          scale: 0.8,
          opacity: 0,
          ease: "back.out(1.7)",
          stagger: 0.1,
        },
        "-=0.3"
      )
      .from(
        ".hero-section .grid",
        {
          duration: 1,
          y: 50,
          opacity: 0,
          ease: "power2.out",
        },
        "-=0.5"
      );

    // Profile image animation
    gsap.from(".hero-section img", {
      duration: 1.5,
      scale: 0.8,
      opacity: 0,
      rotation: 10,
      ease: "power3.out",
      delay: 1,
    });

    // Floating elements animation
    gsap.from(".hero-section .absolute", {
      duration: 1,
      scale: 0,
      opacity: 0,
      ease: "back.out(1.7)",
      stagger: 0.2,
      delay: 1.5,
    });
  }

  setupTypingAnimation() {
    const texts = [
      "Data Analyst",
      "Graphic Designer",
      "Web Developer",
      "Financial Expert",
      "Creative Problem Solver",
    ];

    let currentIndex = 0;
    const typedElement = document.getElementById("typed-text");

    if (!typedElement) return;

    const typeText = () => {
      const currentText = texts[currentIndex];

      gsap.to(typedElement, {
        duration: 0.05 * currentText.length,
        text: currentText,
        ease: "none",
        onComplete: () => {
          gsap.delayedCall(2, () => {
            gsap.to(typedElement, {
              duration: 0.03 * currentText.length,
              text: "",
              ease: "none",
              onComplete: () => {
                currentIndex = (currentIndex + 1) % texts.length;
                typeText();
              },
            });
          });
        },
      });
    };

    gsap.delayedCall(2, typeText);
  }

  setupScrollTriggers() {
    // Parallax background elements
    gsap.utils.toArray(".absolute").forEach((element, i) => {
      gsap.to(element, {
        yPercent: -50 * (i + 1),
        ease: "none",
        scrollTrigger: {
          trigger: element,
          start: "top bottom",
          end: "bottom top",
          scrub: true,
        },
      });
    });

    // Section reveal animations
    gsap.utils.toArray("section").forEach((section) => {
      gsap.from(section, {
        y: 100,
        opacity: 0,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: section,
          start: "top 80%",
          end: "top 20%",
          toggleActions: "play none none reverse",
        },
      });
    });
  }

  setupSkillBars() {
    gsap.utils.toArray(".skill-fill").forEach((bar) => {
      const width = bar.style.width;

      gsap.set(bar, { width: "0%" });

      gsap.to(bar, {
        width: width,
        duration: 1.5,
        ease: "power2.out",
        scrollTrigger: {
          trigger: bar,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      });
    });
  }

  setupProjectCards() {
    gsap.utils.toArray(".project-card").forEach((card, i) => {
      // Card entrance animation
      gsap.from(card, {
        y: 100,
        opacity: 0,
        rotation: 5,
        duration: 1,
        ease: "power3.out",
        delay: i * 0.1,
        scrollTrigger: {
          trigger: card,
          start: "top 85%",
          toggleActions: "play none none reverse",
        },
      });

      // Hover animations
      card.addEventListener("mouseenter", () => {
        gsap.to(card, {
          y: -10,
          scale: 1.02,
          duration: 0.3,
          ease: "power2.out",
        });

        gsap.to(card.querySelector(".project-overlay"), {
          opacity: 1,
          duration: 0.3,
          ease: "power2.out",
        });
      });

      card.addEventListener("mouseleave", () => {
        gsap.to(card, {
          y: 0,
          scale: 1,
          duration: 0.3,
          ease: "power2.out",
        });

        gsap.to(card.querySelector(".project-overlay"), {
          opacity: 0,
          duration: 0.3,
          ease: "power2.out",
        });
      });
    });
  }

  setupGlitchEffects() {
    // Random glitch effect on title
    const glitchElements = gsap.utils.toArray(".glitch");

    glitchElements.forEach((element) => {
      const glitchTl = gsap.timeline({ repeat: -1, repeatDelay: 3 });

      glitchTl
        .to(element, {
          duration: 0.1,
          skewX: 10,
          ease: "power2.inOut",
        })
        .to(element, {
          duration: 0.05,
          skewX: -5,
          ease: "power2.inOut",
        })
        .to(element, {
          duration: 0.05,
          skewX: 0,
          ease: "power2.inOut",
        });
    });
  }

  // Utility methods
  animateIn(element, options = {}) {
    const defaults = {
      y: 50,
      opacity: 0,
      duration: 1,
      ease: "power3.out",
    };

    return gsap.from(element, { ...defaults, ...options });
  }

  animateOut(element, options = {}) {
    const defaults = {
      y: -50,
      opacity: 0,
      duration: 0.5,
      ease: "power2.in",
    };

    return gsap.to(element, { ...defaults, ...options });
  }

  // Advanced Animation Methods
  setupAdvancedAnimations() {
    // Magnetic cursor effect for interactive elements
    const magneticElements = document.querySelectorAll(".magnetic");

    magneticElements.forEach((element) => {
      element.addEventListener("mouseenter", () => {
        gsap.to(element, {
          scale: 1.1,
          duration: 0.3,
          ease: "power2.out",
        });
      });

      element.addEventListener("mouseleave", () => {
        gsap.to(element, {
          scale: 1,
          x: 0,
          y: 0,
          duration: 0.3,
          ease: "power2.out",
        });
      });

      element.addEventListener("mousemove", (e) => {
        const rect = element.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;

        gsap.to(element, {
          x: x * 0.3,
          y: y * 0.3,
          duration: 0.3,
          ease: "power2.out",
        });
      });
    });
  }

  setupMicroInteractions() {
    // Button hover animations
    const buttons = document.querySelectorAll(
      ".btn, .project-link, .cta-button"
    );

    buttons.forEach((button) => {
      button.addEventListener("mouseenter", () => {
        gsap.to(button, {
          scale: 1.05,
          boxShadow: "0 10px 30px rgba(0, 255, 255, 0.3)",
          duration: 0.3,
          ease: "power2.out",
        });
      });

      button.addEventListener("mouseleave", () => {
        gsap.to(button, {
          scale: 1,
          boxShadow: "0 0 0 rgba(0, 255, 255, 0)",
          duration: 0.3,
          ease: "power2.out",
        });
      });
    });

    // Card tilt effect
    const cards = document.querySelectorAll(
      ".project-card, .skill-card, .cyber-card"
    );

    cards.forEach((card) => {
      card.addEventListener("mousemove", (e) => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const centerX = rect.width / 2;
        const centerY = rect.height / 2;

        const rotateX = (y - centerY) / 10;
        const rotateY = (centerX - x) / 10;

        gsap.to(card, {
          rotateX: rotateX,
          rotateY: rotateY,
          transformPerspective: 1000,
          duration: 0.3,
          ease: "power2.out",
        });
      });

      card.addEventListener("mouseleave", () => {
        gsap.to(card, {
          rotateX: 0,
          rotateY: 0,
          duration: 0.3,
          ease: "power2.out",
        });
      });
    });
  }

  setupParallaxEffects() {
    // Advanced parallax for background elements
    const parallaxElements = document.querySelectorAll(".parallax-element");

    parallaxElements.forEach((element, index) => {
      const speed = element.dataset.speed || 0.5;

      gsap.to(element, {
        yPercent: -50 * speed,
        ease: "none",
        scrollTrigger: {
          trigger: element,
          start: "top bottom",
          end: "bottom top",
          scrub: true,
        },
      });
    });

    // Text reveal on scroll
    const textElements = document.querySelectorAll(".reveal-text");

    textElements.forEach((element) => {
      gsap.fromTo(
        element,
        {
          y: 100,
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
          },
        }
      );
    });
  }

  setupMorphingElements() {
    // SVG morphing animations
    const morphElements = document.querySelectorAll(".morph-svg");

    morphElements.forEach((element) => {
      const paths = element.querySelectorAll("path");
      if (paths.length >= 2) {
        gsap.to(paths[0], {
          morphSVG: paths[1],
          duration: 2,
          ease: "power2.inOut",
          repeat: -1,
          yoyo: true,
        });
      }
    });

    // Loading animations
    this.setupLoadingAnimations();
  }

  setupLoadingAnimations() {
    // Page load animation
    const loadingScreen = document.querySelector(".loading-screen");
    if (loadingScreen) {
      const tl = gsap.timeline();

      tl.to(".loading-progress", {
        width: "100%",
        duration: 2,
        ease: "power2.inOut",
      })
        .to(loadingScreen, {
          opacity: 0,
          duration: 0.5,
          ease: "power2.inOut",
        })
        .to(loadingScreen, {
          display: "none",
          duration: 0,
        });
    }
  }

  // Cleanup method
  destroy() {
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    gsap.globalTimeline.clear();
  }
}

export default AnimationController;
