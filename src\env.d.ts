/// <reference path="../.astro/types.d.ts" />
/// <reference types="astro/client" />

// Global type declarations
declare global {
  interface Window {
    gsap: any;
    lenis: any;
  }
}

// Component Props Types
export interface BaseProps {
  class?: string;
  id?: string;
}

export interface LayoutProps {
  title: string;
  description?: string;
  image?: string;
  canonical?: string;
}

export interface SkillData {
  name: string;
  level: number;
  category: 'accounting' | 'design' | 'data' | 'web' | 'tools' | 'soft';
}

export interface ProjectData {
  id: string;
  title: string;
  description: string;
  image?: string;
  tags: string[];
  links: {
    demo?: string;
    github?: string;
    live?: string;
  };
  category: 'dashboard' | 'design' | 'analysis' | 'web' | 'accounting' | 'visualization';
}

export interface ContactInfo {
  email: string;
  linkedin: string;
  instagram: string;
  location: string;
}

// Animation Types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  ease?: string;
}

export interface ScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
}

// Form Types
export interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Utility Types
export type Theme = 'cyberpunk' | 'dark' | 'light';
export type DeviceType = 'mobile' | 'tablet' | 'desktop';
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Astro specific types
declare module 'astro:content' {
  interface ContentEntryMap {}
}

export {};
