#!/usr/bin/env node

/**
 * Accessibility Testing Script
 * Tests the website for WCAG compliance and accessibility issues
 */

import { chromium } from 'playwright';
import { AxeBuilder } from '@axe-core/playwright';
import fs from 'fs/promises';
import path from 'path';

const BASE_URL = 'http://localhost:4321';
const PAGES_TO_TEST = [
  '/',
  '/projects',
];

const ACCESSIBILITY_STANDARDS = {
  wcag2a: ['wcag2a'],
  wcag2aa: ['wcag2aa'],
  wcag21aa: ['wcag21aa'],
  section508: ['section508'],
};

class AccessibilityTester {
  constructor() {
    this.browser = null;
    this.results = [];
  }

  async init() {
    console.log('🚀 Starting accessibility tests...\n');
    this.browser = await chromium.launch({ headless: true });
  }

  async testPage(url, pageName) {
    console.log(`📄 Testing ${pageName} (${url})`);
    
    const context = await this.browser.newContext();
    const page = await context.newPage();
    
    try {
      await page.goto(url, { waitUntil: 'networkidle' });
      
      // Wait for dynamic content to load
      await page.waitForTimeout(2000);
      
      // Run axe-core accessibility tests
      const axeBuilder = new AxeBuilder({ page });
      
      // Test against multiple standards
      const results = {};
      
      for (const [standard, tags] of Object.entries(ACCESSIBILITY_STANDARDS)) {
        console.log(`  🔍 Testing ${standard.toUpperCase()}...`);
        
        const result = await axeBuilder
          .withTags(tags)
          .analyze();
        
        results[standard] = {
          violations: result.violations,
          passes: result.passes,
          incomplete: result.incomplete,
          inapplicable: result.inapplicable,
        };
        
        console.log(`    ✅ Passes: ${result.passes.length}`);
        console.log(`    ❌ Violations: ${result.violations.length}`);
        console.log(`    ⚠️  Incomplete: ${result.incomplete.length}`);
      }
      
      // Additional manual checks
      const manualChecks = await this.performManualChecks(page);
      
      const pageResult = {
        url,
        pageName,
        timestamp: new Date().toISOString(),
        results,
        manualChecks,
        summary: this.generateSummary(results),
      };
      
      this.results.push(pageResult);
      
      console.log(`✅ ${pageName} testing completed\n`);
      
    } catch (error) {
      console.error(`❌ Error testing ${pageName}:`, error.message);
    } finally {
      await context.close();
    }
  }

  async performManualChecks(page) {
    const checks = {};
    
    // Check for skip links
    checks.skipLinks = await page.locator('a[href="#main-content"], .skip-link').count() > 0;
    
    // Check for proper heading hierarchy
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents();
    checks.headingHierarchy = this.validateHeadingHierarchy(headings);
    
    // Check for alt text on images
    const imagesWithoutAlt = await page.locator('img:not([alt])').count();
    checks.imageAltText = imagesWithoutAlt === 0;
    
    // Check for form labels
    const inputsWithoutLabels = await page.locator('input:not([aria-label]):not([aria-labelledby])').count();
    const inputsWithLabels = await page.locator('input').count();
    checks.formLabels = inputsWithoutLabels === 0 && inputsWithLabels > 0;
    
    // Check for focus indicators
    checks.focusIndicators = await this.checkFocusIndicators(page);
    
    // Check for color contrast (basic check)
    checks.colorContrast = await this.checkBasicColorContrast(page);
    
    // Check for keyboard navigation
    checks.keyboardNavigation = await this.checkKeyboardNavigation(page);
    
    return checks;
  }

  validateHeadingHierarchy(headings) {
    // Simple check: should start with h1 and not skip levels
    if (headings.length === 0) return false;
    
    const levels = headings.map(h => parseInt(h.match(/^h(\d)/)?.[1] || '1'));
    
    // Should start with h1
    if (levels[0] !== 1) return false;
    
    // Check for level skipping
    for (let i = 1; i < levels.length; i++) {
      if (levels[i] > levels[i-1] + 1) return false;
    }
    
    return true;
  }

  async checkFocusIndicators(page) {
    // Check if focus indicators are visible
    const focusableElements = await page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').count();
    
    if (focusableElements === 0) return true;
    
    // Focus first element and check if it has visible focus
    await page.keyboard.press('Tab');
    const focusedElement = await page.locator(':focus').first();
    
    if (await focusedElement.count() === 0) return false;
    
    // Check if focused element has visible outline or other focus indicator
    const styles = await focusedElement.evaluate(el => {
      const computed = window.getComputedStyle(el);
      return {
        outline: computed.outline,
        outlineWidth: computed.outlineWidth,
        boxShadow: computed.boxShadow,
      };
    });
    
    return styles.outline !== 'none' || 
           styles.outlineWidth !== '0px' || 
           styles.boxShadow !== 'none';
  }

  async checkBasicColorContrast(page) {
    // Basic color contrast check for text elements
    const textElements = await page.locator('p, h1, h2, h3, h4, h5, h6, span, div').all();
    
    for (const element of textElements.slice(0, 10)) { // Check first 10 elements
      try {
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor,
          };
        });
        
        // This is a simplified check - in production, use a proper contrast ratio calculator
        if (styles.color === styles.backgroundColor) {
          return false;
        }
      } catch (error) {
        // Skip elements that can't be evaluated
        continue;
      }
    }
    
    return true;
  }

  async checkKeyboardNavigation(page) {
    // Test basic keyboard navigation
    const initialFocus = await page.locator(':focus').count();
    
    // Press Tab a few times
    for (let i = 0; i < 5; i++) {
      await page.keyboard.press('Tab');
      await page.waitForTimeout(100);
    }
    
    const finalFocus = await page.locator(':focus').count();
    
    // Should be able to navigate with keyboard
    return finalFocus > 0;
  }

  generateSummary(results) {
    const summary = {
      totalViolations: 0,
      totalPasses: 0,
      totalIncomplete: 0,
      criticalIssues: 0,
      seriousIssues: 0,
      moderateIssues: 0,
      minorIssues: 0,
    };
    
    for (const [standard, result] of Object.entries(results)) {
      summary.totalViolations += result.violations.length;
      summary.totalPasses += result.passes.length;
      summary.totalIncomplete += result.incomplete.length;
      
      // Count issues by impact level
      result.violations.forEach(violation => {
        switch (violation.impact) {
          case 'critical':
            summary.criticalIssues++;
            break;
          case 'serious':
            summary.seriousIssues++;
            break;
          case 'moderate':
            summary.moderateIssues++;
            break;
          case 'minor':
            summary.minorIssues++;
            break;
        }
      });
    }
    
    return summary;
  }

  async generateReport() {
    const reportData = {
      testRun: {
        timestamp: new Date().toISOString(),
        baseUrl: BASE_URL,
        pagesTestedCount: this.results.length,
      },
      summary: this.generateOverallSummary(),
      pages: this.results,
    };
    
    // Generate HTML report
    const htmlReport = this.generateHTMLReport(reportData);
    
    // Ensure reports directory exists
    await fs.mkdir('reports', { recursive: true });
    
    // Save JSON report
    await fs.writeFile(
      path.join('reports', 'accessibility-report.json'),
      JSON.stringify(reportData, null, 2)
    );
    
    // Save HTML report
    await fs.writeFile(
      path.join('reports', 'accessibility-report.html'),
      htmlReport
    );
    
    console.log('📊 Reports generated:');
    console.log('  - reports/accessibility-report.json');
    console.log('  - reports/accessibility-report.html');
  }

  generateOverallSummary() {
    const overall = {
      totalViolations: 0,
      totalPasses: 0,
      criticalIssues: 0,
      seriousIssues: 0,
      moderateIssues: 0,
      minorIssues: 0,
      pagesWithIssues: 0,
    };
    
    this.results.forEach(page => {
      overall.totalViolations += page.summary.totalViolations;
      overall.totalPasses += page.summary.totalPasses;
      overall.criticalIssues += page.summary.criticalIssues;
      overall.seriousIssues += page.summary.seriousIssues;
      overall.moderateIssues += page.summary.moderateIssues;
      overall.minorIssues += page.summary.minorIssues;
      
      if (page.summary.totalViolations > 0) {
        overall.pagesWithIssues++;
      }
    });
    
    return overall;
  }

  generateHTMLReport(data) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .page-result { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .violation { background: #ffe6e6; padding: 10px; margin: 10px 0; border-left: 4px solid #ff4444; }
        .pass { background: #e6ffe6; padding: 10px; margin: 10px 0; border-left: 4px solid #44ff44; }
        .critical { border-left-color: #ff0000; }
        .serious { border-left-color: #ff8800; }
        .moderate { border-left-color: #ffaa00; }
        .minor { border-left-color: #ffdd00; }
    </style>
</head>
<body>
    <h1>Accessibility Test Report</h1>
    <div class="summary">
        <h2>Overall Summary</h2>
        <p><strong>Test Date:</strong> ${data.testRun.timestamp}</p>
        <p><strong>Pages Tested:</strong> ${data.testRun.pagesTestedCount}</p>
        <p><strong>Total Violations:</strong> ${data.summary.totalViolations}</p>
        <p><strong>Total Passes:</strong> ${data.summary.totalPasses}</p>
        <p><strong>Critical Issues:</strong> ${data.summary.criticalIssues}</p>
        <p><strong>Serious Issues:</strong> ${data.summary.seriousIssues}</p>
        <p><strong>Moderate Issues:</strong> ${data.summary.moderateIssues}</p>
        <p><strong>Minor Issues:</strong> ${data.summary.minorIssues}</p>
    </div>
    
    ${data.pages.map(page => `
        <div class="page-result">
            <h2>${page.pageName} (${page.url})</h2>
            <h3>Summary</h3>
            <p>Violations: ${page.summary.totalViolations} | Passes: ${page.summary.totalPasses}</p>
            
            <h3>Manual Checks</h3>
            <ul>
                ${Object.entries(page.manualChecks).map(([check, passed]) => 
                    `<li>${check}: ${passed ? '✅ Pass' : '❌ Fail'}</li>`
                ).join('')}
            </ul>
            
            ${Object.entries(page.results).map(([standard, result]) => `
                <h3>${standard.toUpperCase()} Results</h3>
                ${result.violations.map(violation => `
                    <div class="violation ${violation.impact}">
                        <strong>${violation.id}</strong> (${violation.impact})<br>
                        ${violation.description}<br>
                        <small>Affected elements: ${violation.nodes.length}</small>
                    </div>
                `).join('')}
            `).join('')}
        </div>
    `).join('')}
</body>
</html>`;
  }

  async run() {
    try {
      await this.init();
      
      for (const pagePath of PAGES_TO_TEST) {
        const url = `${BASE_URL}${pagePath}`;
        const pageName = pagePath === '/' ? 'Home' : pagePath.replace('/', '');
        await this.testPage(url, pageName);
      }
      
      await this.generateReport();
      
      const summary = this.generateOverallSummary();
      
      console.log('\n🎯 Test Summary:');
      console.log(`   Total Violations: ${summary.totalViolations}`);
      console.log(`   Critical Issues: ${summary.criticalIssues}`);
      console.log(`   Serious Issues: ${summary.seriousIssues}`);
      console.log(`   Pages with Issues: ${summary.pagesWithIssues}/${PAGES_TO_TEST.length}`);
      
      if (summary.criticalIssues > 0) {
        console.log('\n❌ Critical accessibility issues found!');
        process.exit(1);
      } else if (summary.seriousIssues > 0) {
        console.log('\n⚠️  Serious accessibility issues found.');
        process.exit(1);
      } else {
        console.log('\n✅ No critical accessibility issues found!');
      }
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      process.exit(1);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new AccessibilityTester();
  tester.run();
}

export default AccessibilityTester;
