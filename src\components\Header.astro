---
import type { BaseProps } from "@/env.d.ts";

export interface Props extends BaseProps {}

const { class: className } = Astro.props;

// Navigation items with proper typing
const navItems = [
  { href: "#home", label: "HOME", ariaLabel: "Navigate to home section" },
  { href: "#about", label: "ABOUT", ariaLabel: "Navigate to about section" },
  { href: "#skills", label: "SKILLS", ariaLabel: "Navigate to skills section" },
  {
    href: "#projects",
    label: "PROJECTS",
    ariaLabel: "Navigate to projects section",
  },
  { href: "/blog", label: "BLOG", ariaLabel: "Navigate to blog section" },
  {
    href: "#playground",
    label: "PLAYGROUND",
    ariaLabel: "Navigate to code playground section",
  },
  {
    href: "#contact",
    label: "CONTACT",
    ariaLabel: "Navigate to contact section",
  },
] as const;
---

<header
  class={`fixed top-0 left-0 right-0 z-50 bg-black/80 backdrop-blur-md border-b border-cyan-500/30 ${className || ""}`}
>
  <nav
    class="container mx-auto px-6 py-4"
    role="navigation"
    aria-label="Main navigation"
  >
    <div class="flex items-center justify-between">
      <!-- Logo -->
      <div class="logo">
        <a
          href="/"
          class="text-2xl font-bold text-cyan-400 neon-glow glitch focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
          aria-label="Trinanda - Go to homepage"
        >
          &lt;TRINANDA/&gt;
        </a>
      </div>

      <!-- Desktop Navigation -->
      <ul class="hidden md:flex space-x-8" role="menubar">
        {
          navItems.map((item) => (
            <li role="none">
              <a
                href={item.href}
                class="nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
                role="menuitem"
                aria-label={item.ariaLabel}
              >
                {item.label}
              </a>
            </li>
          ))
        }
      </ul>

      <!-- Mobile Menu Button -->
      <button
        id="mobile-menu-btn"
        class="md:hidden text-cyan-400 hover:text-pink-400 transition-colors focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm p-1"
        aria-label="Toggle mobile menu"
        aria-expanded="false"
        aria-controls="mobile-menu"
        type="button"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div
      id="mobile-menu"
      class="md:hidden hidden mt-4 pb-4"
      role="menu"
      aria-labelledby="mobile-menu-btn"
    >
      <ul class="space-y-4">
        {
          navItems.map((item) => (
            <li role="none">
              <a
                href={item.href}
                class="mobile-nav-link focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black rounded-sm"
                role="menuitem"
                aria-label={item.ariaLabel}
              >
                {item.label}
              </a>
            </li>
          ))
        }
      </ul>
    </div>
  </nav>
</header>

<style>
  .nav-link {
    @apply text-gray-300 hover:text-cyan-400 transition-all duration-300 relative;
    font-family: "Orbitron", monospace;
    font-weight: 500;
    letter-spacing: 1px;
  }

  .nav-link:hover {
    text-shadow: 0 0 10px currentColor;
  }

  .nav-link::after {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-cyan), var(--neon-pink));
    transition: width 0.3s ease;
  }

  .nav-link:hover::after {
    width: 100%;
  }

  .mobile-nav-link {
    @apply block text-gray-300 hover:text-cyan-400 transition-colors py-2 border-b border-gray-700/50;
    font-family: "Orbitron", monospace;
    font-weight: 500;
    letter-spacing: 1px;
  }

  .mobile-nav-link:hover {
    text-shadow: 0 0 10px currentColor;
  }
</style>

<script>
  // Mobile menu toggle
  const mobileMenuBtn = document.getElementById("mobile-menu-btn");
  const mobileMenu = document.getElementById("mobile-menu");

  mobileMenuBtn?.addEventListener("click", () => {
    mobileMenu?.classList.toggle("hidden");
  });

  // Smooth scrolling for navigation links
  document.querySelectorAll('a[href^="#"]').forEach((anchor: Element) => {
    anchor.addEventListener("click", (e: Event) => {
      e.preventDefault();
      const href = (anchor as HTMLAnchorElement).getAttribute("href");
      const target = href ? document.querySelector(href) : null;
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        // Close mobile menu if open
        mobileMenu?.classList.add("hidden");
      }
    });
  });

  // Header background opacity on scroll
  window.addEventListener("scroll", () => {
    const header = document.querySelector("header");
    if (window.scrollY > 100) {
      header?.classList.add("bg-black/90");
      header?.classList.remove("bg-black/80");
    } else {
      header?.classList.add("bg-black/80");
      header?.classList.remove("bg-black/90");
    }
  });
</script>
