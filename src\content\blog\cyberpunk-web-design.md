---
title: "Creating a Cyberpunk-Themed Portfolio with Astro and Three.js"
description: "Learn how to build a futuristic, cyberpunk-inspired portfolio website using modern web technologies like Astro, Three.js, and GSAP animations."
publishedAt: 2024-01-15T00:00:00Z
image: "/blog/cyberpunk-design.jpg"
imageAlt: "Cyberpunk-themed portfolio website with neon effects"
tags: ["astro", "three.js", "web-design", "cyberpunk", "portfolio"]
category: "Web Design"
featured: true
readingTime: 8
author: "Muhammad Trinanda"
---

# Creating a Cyberpunk-Themed Portfolio with Astro and Three.js

In this comprehensive guide, I'll walk you through the process of creating a futuristic, cyberpunk-inspired portfolio website that combines cutting-edge web technologies with stunning visual effects.

## Why Cyberpunk Design?

The cyberpunk aesthetic has become increasingly popular in web design due to its:

- **Visual Impact**: Neon colors and glitch effects create memorable experiences
- **Modern Appeal**: Reflects our digital-first world
- **Versatility**: Works well for tech portfolios and creative projects
- **Engagement**: Interactive elements keep visitors interested

## Technology Stack

For this project, I chose a modern stack that prioritizes performance and developer experience:

### Core Framework: Astro 5

- **Static Site Generation**: Lightning-fast loading times
- **Islands Architecture**: Selective hydration for optimal performance
- **TypeScript Support**: Type safety and better developer experience
- **Built-in Optimizations**: Image optimization, code splitting, and more

### 3D Graphics: Three.js

- **Particle Systems**: Dynamic background effects
- **WebGL Performance**: Hardware-accelerated graphics
- **Interactive Elements**: Mouse-responsive animations
- **Cross-browser Support**: Consistent experience across devices

### Animations: GSAP

- **Smooth Transitions**: Professional-grade animations
- **ScrollTrigger**: Scroll-based animations
- **Performance**: Optimized for 60fps animations
- **Flexibility**: Easy to customize and extend

## Key Features Implemented

### 1. Dynamic Particle Background

```javascript
// Particle system with custom shaders
const particleSystem = new THREE.Points(geometry, material);
scene.add(particleSystem);
```

### 2. Responsive Design

- Mobile-first approach
- Flexible grid layouts
- Touch-friendly interactions
- Optimized for all screen sizes

### 3. Performance Optimizations

- Image compression and WebP format
- Code splitting and lazy loading
- Service worker for caching
- Critical CSS inlining

### 4. Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility

## Design Principles

### Color Palette

The cyberpunk theme relies on a carefully chosen color palette:

- **Primary**: Cyan (#00FFFF) - High-tech, digital feel
- **Secondary**: Magenta (#FF00FF) - Energy and creativity
- **Accent**: Neon Green (#00FF00) - Matrix-inspired elements
- **Background**: Deep Black (#0A0A0A) - Creates contrast

### Typography

- **Headers**: Orbitron - Futuristic, geometric font
- **Body**: Courier New - Monospace for code-like appearance
- **Accents**: Custom glitch effects and animations

### Visual Effects

1. **Neon Glow**: CSS text-shadow and box-shadow
2. **Glitch Animation**: CSS transforms and keyframes
3. **Matrix Rain**: Canvas-based falling characters
4. **Particle Effects**: Three.js WebGL rendering

## Performance Considerations

### Optimization Strategies

- **Image Optimization**: 67% reduction in file sizes
- **Code Splitting**: Separate chunks for vendor libraries
- **Lazy Loading**: Components load when needed
- **Caching Strategy**: Service worker implementation

### Core Web Vitals Results

- **LCP**: < 2.5s (Good)
- **FID**: < 100ms (Good)
- **CLS**: < 0.1 (Good)

## Lessons Learned

### What Worked Well

1. **Astro's Performance**: Excellent out-of-the-box optimizations
2. **Three.js Integration**: Smooth WebGL performance
3. **GSAP Animations**: Professional animation quality
4. **Component Architecture**: Reusable and maintainable code

### Challenges Faced

1. **Mobile Performance**: Required careful optimization
2. **Browser Compatibility**: WebGL support variations
3. **Accessibility**: Balancing effects with usability
4. **Loading Times**: Managing large asset files

## Future Enhancements

### Planned Features

- [ ] Dark/Light mode toggle
- [ ] Blog system integration
- [ ] Contact form functionality
- [ ] Advanced particle interactions
- [ ] VR/AR compatibility

### Technical Improvements

- [ ] Progressive Web App features
- [ ] Advanced caching strategies
- [ ] Real-time analytics
- [ ] A/B testing framework

## Conclusion

Building a cyberpunk-themed portfolio has been an exciting journey that combined creative design with technical excellence. The result is a website that not only showcases my skills but also provides an engaging user experience.

The key to success was balancing visual impact with performance, ensuring that the stunning effects don't compromise usability or loading times.

## Resources and Links

- [Astro Documentation](https://docs.astro.build/)
- [Three.js Examples](https://threejs.org/examples/)
- [GSAP Documentation](https://greensock.com/docs/)
- [Cyberpunk Color Palettes](https://coolors.co/palettes/trending/cyberpunk)

---

_Want to see the code behind this project? Check out the [GitHub repository](https://github.com/trinanda/cyberpunk-portfolio) for the complete source code and implementation details._
