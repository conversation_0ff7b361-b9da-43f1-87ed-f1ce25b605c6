---
// Critical CSS component for above-the-fold content
---

<style is:inline>
  /* Critical CSS - Above the fold styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    background: #0a0a0a;
    color: #ffffff;
    font-family: "Orbitron", "Courier New", monospace;
    overflow-x: hidden;
    line-height: 1.6;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  /* Header critical styles */
  header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  }

  /* Hero section critical styles */
  .hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding-top: 5rem;
  }

  /* Neon glow effect */
  .neon-glow {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(45deg, #00ffff, #ff00ff, #8a2be2);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Glitch effect */
  .glitch {
    position: relative;
    animation: glitch 2s infinite;
  }

  @keyframes glitch {
    0%,
    100% {
      transform: translate(0);
    }
    20% {
      transform: translate(-2px, 2px);
    }
    40% {
      transform: translate(-2px, -2px);
    }
    60% {
      transform: translate(2px, 2px);
    }
    80% {
      transform: translate(2px, -2px);
    }
  }

  /* Loading state */
  .loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
  }

  .loaded {
    opacity: 1;
    transform: translateY(0);
  }

  /* Cyber grid background */
  .cyber-grid {
    background-image:
      linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Matrix canvas */
  #matrix-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -10;
    pointer-events: none;
    opacity: 0.1;
  }

  /* Fix footer gap and bottom space completely */
  footer {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Ensure no extra space at bottom */
  html,
  body {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    height: 100%;
    overflow-x: hidden;
  }

  /* Remove space from main containers */
  main {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Ensure layout containers don't add space */
  .relative.z-10 {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Force body to use flexbox layout */
  body {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
  }

  /* Make main content flexible */
  main {
    flex: 1 !important;
  }

  /* Responsive typography */
  @media (max-width: 768px) {
    .hero-section {
      padding: 6rem 1rem 2rem;
    }
  }
</style>

<script is:inline>
  // Critical JavaScript for immediate functionality
  (function () {
    // Add loaded class when DOM is ready
    document.addEventListener("DOMContentLoaded", function () {
      document.body.classList.add("loaded");

      // Remove loading states
      const loadingElements = document.querySelectorAll(".loading");
      loadingElements.forEach(function (el) {
        el.classList.remove("loading");
        el.classList.add("loaded");
      });
    });

    // Prevent FOUC (Flash of Unstyled Content)
    document.documentElement.style.visibility = "visible";
  })();
</script>
