[{"id": "javascript", "name": "JavaScript", "category": "frontend", "level": 90, "icon": "devicon:javascript", "description": "Modern JavaScript ES6+ with advanced concepts", "experience": "3+ years", "certifications": ["JavaScript Algorithms and Data Structures"], "projects": ["cyberpunk-portfolio", "dashboard-analytics"], "lastUsed": "2024-01-15"}, {"id": "typescript", "name": "TypeScript", "category": "frontend", "level": 85, "icon": "devicon:typescript", "description": "Type-safe JavaScript development", "experience": "2+ years", "certifications": [], "projects": ["cyberpunk-portfolio"], "lastUsed": "2024-01-15"}, {"id": "astro", "name": "Astro", "category": "frontend", "level": 88, "icon": "devicon:astro", "description": "Modern static site generator with islands architecture", "experience": "1+ years", "certifications": [], "projects": ["cyberpunk-portfolio"], "lastUsed": "2024-01-15"}, {"id": "react", "name": "React", "category": "frontend", "level": 82, "icon": "devicon:react", "description": "Component-based UI library", "experience": "2+ years", "certifications": [], "projects": ["dashboard-analytics"], "lastUsed": "2024-01-10"}, {"id": "tailwindcss", "name": "TailwindCSS", "category": "frontend", "level": 92, "icon": "devicon:tailwindcss", "description": "Utility-first CSS framework", "experience": "2+ years", "certifications": [], "projects": ["cyberpunk-portfolio", "dashboard-analytics"], "lastUsed": "2024-01-15"}, {"id": "threejs", "name": "Three.js", "category": "frontend", "level": 75, "icon": "devicon:threejs", "description": "3D graphics library for web", "experience": "1+ years", "certifications": [], "projects": ["cyberpunk-portfolio"], "lastUsed": "2024-01-15"}, {"id": "gsap", "name": "GSAP", "category": "frontend", "level": 80, "icon": "simple-icons:greensock", "description": "Professional-grade animation library", "experience": "1+ years", "certifications": [], "projects": ["cyberpunk-portfolio"], "lastUsed": "2024-01-15"}, {"id": "nodejs", "name": "Node.js", "category": "backend", "level": 78, "icon": "devicon:nodejs", "description": "JavaScript runtime for server-side development", "experience": "2+ years", "certifications": [], "projects": ["api-services"], "lastUsed": "2024-01-12"}, {"id": "python", "name": "Python", "category": "backend", "level": 85, "icon": "devicon:python", "description": "Data analysis and automation", "experience": "3+ years", "certifications": ["Python for Data Science"], "projects": ["data-analysis-dashboard"], "lastUsed": "2024-01-14"}, {"id": "postgresql", "name": "PostgreSQL", "category": "database", "level": 70, "icon": "devicon:postgresql", "description": "Advanced relational database", "experience": "1+ years", "certifications": [], "projects": ["data-analysis-dashboard"], "lastUsed": "2024-01-10"}, {"id": "mongodb", "name": "MongoDB", "category": "database", "level": 72, "icon": "devicon:mongodb", "description": "NoSQL document database", "experience": "1+ years", "certifications": [], "projects": ["content-management"], "lastUsed": "2024-01-08"}, {"id": "git", "name": "Git", "category": "tools", "level": 88, "icon": "devicon:git", "description": "Version control system", "experience": "3+ years", "certifications": [], "projects": ["all-projects"], "lastUsed": "2024-01-15"}, {"id": "vscode", "name": "VS Code", "category": "tools", "level": 90, "icon": "devicon:vscode", "description": "Code editor and development environment", "experience": "3+ years", "certifications": [], "projects": ["all-projects"], "lastUsed": "2024-01-15"}, {"id": "figma", "name": "Figma", "category": "design", "level": 85, "icon": "devicon:figma", "description": "UI/UX design and prototyping", "experience": "2+ years", "certifications": [], "projects": ["cyberpunk-portfolio", "dashboard-analytics"], "lastUsed": "2024-01-13"}, {"id": "photoshop", "name": "Adobe Photoshop", "category": "design", "level": 80, "icon": "devicon:photoshop", "description": "Image editing and graphic design", "experience": "3+ years", "certifications": [], "projects": ["branding-projects"], "lastUsed": "2024-01-11"}, {"id": "excel", "name": "Microsoft Excel", "category": "finance", "level": 92, "icon": "vscode-icons:file-type-excel", "description": "Advanced spreadsheet analysis and financial modeling", "experience": "4+ years", "certifications": ["Excel Expert Certification"], "projects": ["financial-analysis", "data-visualization"], "lastUsed": "2024-01-14"}, {"id": "accounting", "name": "Sharia Accounting", "category": "finance", "level": 88, "icon": "mdi:calculator", "description": "Islamic accounting principles and practices", "experience": "3+ years", "certifications": ["Sharia Accounting Fundamentals"], "projects": ["financial-reports"], "lastUsed": "2024-01-12"}, {"id": "communication", "name": "Communication", "category": "soft-skills", "level": 85, "icon": "mdi:account-voice", "description": "Effective verbal and written communication", "experience": "4+ years", "certifications": [], "projects": ["all-projects"], "lastUsed": "2024-01-15"}, {"id": "problem-solving", "name": "Problem Solving", "category": "soft-skills", "level": 90, "icon": "mdi:puzzle", "description": "Analytical thinking and creative solutions", "experience": "4+ years", "certifications": [], "projects": ["all-projects"], "lastUsed": "2024-01-15"}, {"id": "teamwork", "name": "Teamwork", "category": "soft-skills", "level": 88, "icon": "mdi:account-group", "description": "Collaborative work and team leadership", "experience": "3+ years", "certifications": [], "projects": ["team-projects"], "lastUsed": "2024-01-14"}]