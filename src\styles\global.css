/* Custom Fonts Import */
@import url("/fonts/orbitron.css");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Cyberpunk Theme Variables */
:root {
  /* Neon Colors */
  --neon-cyan: #00ffff;
  --neon-pink: #ff00ff;
  --neon-green: #00ff00;
  --neon-purple: #8a2be2;
  --neon-orange: #ff6600;
  --neon-blue: #0066ff;

  /* Dark Theme Colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-accent: var(--neon-cyan);

  /* Gradients */
  --gradient-cyber: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
  --gradient-dark: linear-gradient(
    135deg,
    var(--bg-primary),
    var(--bg-secondary)
  );
}

/* Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-x: hidden;
}

body {
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: "Orbitron", "Courier New", monospace;
  overflow-x: hidden;
  line-height: 1.6;
  min-height: 100vh;
  position: relative;
}

/* Cyberpunk Utilities */
.neon-glow {
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

.neon-border {
  border: 2px solid var(--neon-cyan);
  box-shadow:
    0 0 10px var(--neon-cyan),
    inset 0 0 10px var(--neon-cyan);
}

.cyber-grid {
  background-image:
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

@keyframes glitch {
  0%,
  100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

.pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite alternate;
}

@keyframes pulse-neon {
  from {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px currentColor;
  }
  to {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px currentColor;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 4px;
  box-shadow: 0 0 10px var(--neon-cyan);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-pink);
  box-shadow: 0 0 10px var(--neon-pink);
}

/* Loading Animation */
.cyber-loader {
  width: 50px;
  height: 50px;
  border: 3px solid var(--bg-secondary);
  border-top: 3px solid var(--neon-cyan);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Particle Background */
.particles-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -20;
  pointer-events: none;
  background: radial-gradient(
    ellipse at center,
    rgba(0, 255, 255, 0.02) 0%,
    transparent 70%
  );
}

/* Enhanced Animations */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Scroll Animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.animate-on-scroll.animated {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure main content is always visible */
main,
section,
.hero-section,
.about-section,
.skills-section,
.projects-section,
.contact-section {
  opacity: 1 !important;
  transform: none !important;
}

/* Enhanced Hover Effects */
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 255, 255, 0.2);
}

/* Fix footer gap and bottom space */
footer {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove all bottom spacing */
html, body {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Ensure main content container doesn't add extra space */
main {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove space from layout containers */
.relative.z-10 {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Ensure viewport height is properly utilized */
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
}

/* Remove any pseudo-elements that might add space */
body::after,
html::after,
main::after,
footer::after {
  display: none !important;
}

/* Force remove bottom space with viewport units */
html {
  max-height: 100vh !important;
}

body {
  max-height: 100vh !important;
  overflow-y: auto !important;
}

/* Ensure no margin collapse issues */
* {
  margin-block-end: 0 !important;
  margin-block-start: 0 !important;
}

/* Target specific elements that might cause space */
section:last-child,
div:last-child,
footer {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Remove space from Astro layout wrapper */
[data-astro-cid] {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
