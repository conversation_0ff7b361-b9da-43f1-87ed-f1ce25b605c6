---
export interface Props {
  particleCount?: number;
  enableInteraction?: boolean;
  colorScheme?: 'cyberpunk' | 'matrix' | 'neon';
}

const {
  particleCount = 1000,
  enableInteraction = true,
  colorScheme = 'cyberpunk'
} = Astro.props;
---

<div id="three-background" class="three-background">
  <canvas id="three-canvas"></canvas>
</div>

<style>
  .three-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
  }

  #three-canvas {
    width: 100%;
    height: 100%;
    display: block;
  }
</style>

<script define:vars={{ particleCount, enableInteraction, colorScheme }}>
  import('/src/scripts/three-particles.js').then(({ default: ParticleSystem }) => {
    const container = document.getElementById('three-background');
    if (container) {
      const particles = new ParticleSystem(container);
      
      // Store reference for cleanup
      window.particleSystem = particles;
    }
  });

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    if (window.particleSystem) {
      window.particleSystem.destroy();
    }
  });
</script>
