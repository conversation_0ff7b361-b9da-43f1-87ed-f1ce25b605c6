// Testing and Quality Assurance Utilities

class TestRunner {
  constructor() {
    this.tests = [];
    this.results = [];
    this.isRunning = false;
    this.config = {
      timeout: 5000,
      retries: 0,
      parallel: false,
      verbose: true
    };
  }

  // Test Registration
  describe(suiteName, callback) {
    const suite = {
      name: suiteName,
      tests: [],
      beforeEach: null,
      afterEach: null,
      beforeAll: null,
      afterAll: null
    };

    const context = {
      it: (testName, testFn) => {
        suite.tests.push({
          name: testName,
          fn: testFn,
          suite: suiteName
        });
      },
      beforeEach: (fn) => { suite.beforeEach = fn; },
      afterEach: (fn) => { suite.afterEach = fn; },
      beforeAll: (fn) => { suite.beforeAll = fn; },
      afterAll: (fn) => { suite.afterAll = fn; }
    };

    callback.call(context, context);
    this.tests.push(suite);
  }

  // Test Execution
  async run(pattern = null) {
    if (this.isRunning) {
      throw new Error('Tests are already running');
    }

    this.isRunning = true;
    this.results = [];

    console.log('🧪 Starting test execution...\n');

    try {
      for (const suite of this.tests) {
        if (pattern && !suite.name.includes(pattern)) {
          continue;
        }

        await this.runSuite(suite);
      }

      this.printResults();
    } finally {
      this.isRunning = false;
    }

    return this.getTestSummary();
  }

  async runSuite(suite) {
    console.log(`📋 Running suite: ${suite.name}`);

    if (suite.beforeAll) {
      await this.executeWithTimeout(suite.beforeAll, 'beforeAll');
    }

    for (const test of suite.tests) {
      await this.runTest(test, suite);
    }

    if (suite.afterAll) {
      await this.executeWithTimeout(suite.afterAll, 'afterAll');
    }

    console.log('');
  }

  async runTest(test, suite) {
    const startTime = performance.now();
    let result = {
      suite: suite.name,
      name: test.name,
      status: 'pending',
      duration: 0,
      error: null
    };

    try {
      if (suite.beforeEach) {
        await this.executeWithTimeout(suite.beforeEach, 'beforeEach');
      }

      await this.executeWithTimeout(test.fn, test.name);

      if (suite.afterEach) {
        await this.executeWithTimeout(suite.afterEach, 'afterEach');
      }

      result.status = 'passed';
      console.log(`  ✅ ${test.name}`);
    } catch (error) {
      result.status = 'failed';
      result.error = error;
      console.log(`  ❌ ${test.name}`);
      if (this.config.verbose) {
        console.log(`     Error: ${error.message}`);
      }
    }

    result.duration = performance.now() - startTime;
    this.results.push(result);
  }

  async executeWithTimeout(fn, name) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Test "${name}" timed out after ${this.config.timeout}ms`));
      }, this.config.timeout);

      try {
        const result = fn();
        
        if (result && typeof result.then === 'function') {
          result
            .then(resolve)
            .catch(reject)
            .finally(() => clearTimeout(timeout));
        } else {
          clearTimeout(timeout);
          resolve(result);
        }
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  // Assertion Methods
  expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`Expected ${actual} to be ${expected}`);
        }
      },
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
        }
      },
      toBeTruthy: () => {
        if (!actual) {
          throw new Error(`Expected ${actual} to be truthy`);
        }
      },
      toBeFalsy: () => {
        if (actual) {
          throw new Error(`Expected ${actual} to be falsy`);
        }
      },
      toContain: (expected) => {
        if (!actual.includes(expected)) {
          throw new Error(`Expected ${actual} to contain ${expected}`);
        }
      },
      toThrow: () => {
        let threw = false;
        try {
          actual();
        } catch (e) {
          threw = true;
        }
        if (!threw) {
          throw new Error('Expected function to throw an error');
        }
      }
    };
  }

  // Results and Reporting
  printResults() {
    const summary = this.getTestSummary();
    
    console.log('\n📊 Test Results:');
    console.log(`Total: ${summary.total}`);
    console.log(`Passed: ${summary.passed} ✅`);
    console.log(`Failed: ${summary.failed} ❌`);
    console.log(`Duration: ${summary.duration.toFixed(2)}ms`);
    
    if (summary.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'failed')
        .forEach(result => {
          console.log(`  ${result.suite} > ${result.name}`);
          console.log(`    ${result.error.message}`);
        });
    }
  }

  getTestSummary() {
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const duration = this.results.reduce((sum, r) => sum + r.duration, 0);

    return {
      total: this.results.length,
      passed,
      failed,
      duration,
      success: failed === 0
    };
  }
}

// Accessibility Testing
class AccessibilityTester {
  constructor() {
    this.violations = [];
  }

  async runAccessibilityTests() {
    console.log('🔍 Running accessibility tests...');
    
    this.violations = [];
    
    // Test color contrast
    await this.testColorContrast();
    
    // Test keyboard navigation
    await this.testKeyboardNavigation();
    
    // Test ARIA attributes
    await this.testAriaAttributes();
    
    // Test semantic HTML
    await this.testSemanticHTML();
    
    // Test focus management
    await this.testFocusManagement();
    
    return this.getAccessibilityReport();
  }

  async testColorContrast() {
    const elements = document.querySelectorAll('*');
    
    elements.forEach(element => {
      const styles = window.getComputedStyle(element);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      
      if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
        const contrast = this.calculateContrast(color, backgroundColor);
        
        if (contrast < 4.5) {
          this.violations.push({
            type: 'color-contrast',
            element: element.tagName.toLowerCase(),
            message: `Low color contrast ratio: ${contrast.toFixed(2)}`,
            severity: 'warning'
          });
        }
      }
    });
  }

  async testKeyboardNavigation() {
    const focusableElements = document.querySelectorAll(
      'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach(element => {
      if (!element.hasAttribute('tabindex') && element.tabIndex < 0) {
        this.violations.push({
          type: 'keyboard-navigation',
          element: element.tagName.toLowerCase(),
          message: 'Element is not keyboard accessible',
          severity: 'error'
        });
      }
    });
  }

  async testAriaAttributes() {
    // Test for missing alt text on images
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      if (!img.hasAttribute('alt')) {
        this.violations.push({
          type: 'missing-alt',
          element: 'img',
          message: 'Image missing alt attribute',
          severity: 'error'
        });
      }
    });

    // Test for proper heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let lastLevel = 0;
    
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (level > lastLevel + 1) {
        this.violations.push({
          type: 'heading-hierarchy',
          element: heading.tagName.toLowerCase(),
          message: `Heading level skipped from h${lastLevel} to h${level}`,
          severity: 'warning'
        });
      }
      
      lastLevel = level;
    });
  }

  async testSemanticHTML() {
    // Check for proper landmark usage
    const landmarks = ['main', 'nav', 'header', 'footer', 'aside', 'section'];
    const hasLandmarks = landmarks.some(landmark => 
      document.querySelector(landmark) || 
      document.querySelector(`[role="${landmark}"]`)
    );
    
    if (!hasLandmarks) {
      this.violations.push({
        type: 'semantic-html',
        element: 'document',
        message: 'No semantic landmarks found',
        severity: 'warning'
      });
    }
  }

  async testFocusManagement() {
    // Test for focus traps in modals
    const modals = document.querySelectorAll('[role="dialog"], .modal');
    
    modals.forEach(modal => {
      const focusableElements = modal.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements.length === 0) {
        this.violations.push({
          type: 'focus-management',
          element: 'modal',
          message: 'Modal has no focusable elements',
          severity: 'error'
        });
      }
    });
  }

  calculateContrast(color1, color2) {
    // Simplified contrast calculation
    // In a real implementation, you'd want a more robust color parsing and contrast calculation
    return 4.5; // Placeholder
  }

  getAccessibilityReport() {
    const errors = this.violations.filter(v => v.severity === 'error').length;
    const warnings = this.violations.filter(v => v.severity === 'warning').length;
    
    return {
      violations: this.violations,
      summary: {
        total: this.violations.length,
        errors,
        warnings,
        passed: this.violations.length === 0
      }
    };
  }
}

// Performance Testing
class PerformanceTester {
  constructor() {
    this.metrics = {};
  }

  async runPerformanceTests() {
    console.log('⚡ Running performance tests...');
    
    // Test Core Web Vitals
    await this.measureCoreWebVitals();
    
    // Test resource loading
    await this.measureResourceLoading();
    
    // Test JavaScript performance
    await this.measureJavaScriptPerformance();
    
    return this.getPerformanceReport();
  }

  async measureCoreWebVitals() {
    if ('PerformanceObserver' in window) {
      // Measure LCP
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.lcp = lastEntry.startTime;
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // Measure FID
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.fid = entry.processingStart - entry.startTime;
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Measure CLS
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        this.metrics.cls = clsValue;
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }
  }

  async measureResourceLoading() {
    const resources = performance.getEntriesByType('resource');
    
    this.metrics.resources = {
      total: resources.length,
      totalSize: resources.reduce((sum, r) => sum + (r.transferSize || 0), 0),
      slowResources: resources.filter(r => r.duration > 1000).length,
      averageLoadTime: resources.reduce((sum, r) => sum + r.duration, 0) / resources.length
    };
  }

  async measureJavaScriptPerformance() {
    const startTime = performance.now();
    
    // Simulate some JavaScript work
    for (let i = 0; i < 100000; i++) {
      Math.random();
    }
    
    this.metrics.jsPerformance = {
      executionTime: performance.now() - startTime,
      memoryUsage: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null
    };
  }

  getPerformanceReport() {
    const issues = [];
    
    if (this.metrics.lcp > 4000) {
      issues.push('Poor Largest Contentful Paint');
    }
    
    if (this.metrics.fid > 300) {
      issues.push('Poor First Input Delay');
    }
    
    if (this.metrics.cls > 0.25) {
      issues.push('Poor Cumulative Layout Shift');
    }
    
    return {
      metrics: this.metrics,
      issues,
      passed: issues.length === 0
    };
  }
}

// Export classes
export { TestRunner, AccessibilityTester, PerformanceTester };

// Auto-initialize testing utilities
if (typeof window !== 'undefined') {
  window.testRunner = new TestRunner();
  window.accessibilityTester = new AccessibilityTester();
  window.performanceTester = new PerformanceTester();
}
