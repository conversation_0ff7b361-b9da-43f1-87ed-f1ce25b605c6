/* style.css */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #663399;
    --secondary-color: #8058a5;
    --text-color: #333;
    --background-color: #f4f4f4;
    --light-gray: #eee;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    overflow-x: hidden;
     /* Make body a flex container */
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* Minimum height of viewport */
}

/* Header dan Navigasi */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    padding: 1rem 0;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    font-weight: bold;
    font-size: 1.2rem;
}

.nav-links {
    list-style: none;
    display: flex;
}

.nav-links li {
    margin-left: 20px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    padding: 8px 15px;
    border-radius: 5px;
    transition: background-color 0.3s ease, color 0.3s ease;
    display: block;
}

.nav-links a:hover {
    background-color: var(--primary-color);
    color: #fff;
}

/* Hamburger Menu */
.hamburger-menu {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
    padding: 10px;
    z-index: 1000;
}

.hamburger-menu i {
    color: black;
    transition: transform 0.3s ease;
}

/* --- Loading Animation (loading.io) --- */
/*
 *  Bagian ini menggunakan library loading bar dari loading.io.
 *  Library ini memungkinkan kita membuat animasi loading kustom dengan mudah
 *  menggunakan atribut data-*.  Kode di bawah ini mengkonfigurasi
 *  tampilan dan perilaku loading bar.
 *
 *  Sumber: https://loading.io/
 *
 *  Cara Kerja:
 *  1.  Elemen HTML dengan class `ldBar` dan ID `my-loader` digunakan sebagai
 *      container untuk loading bar.
 *  2.  Atribut `data-*` pada elemen ini digunakan untuk mengatur:
 *      - `data-preset`:  Jenis animasi (misalnya, "circle").
 *      - `data-value`:  Nilai awal progress (0-100).
 *      - `data-fill`:  Warna pengisi (bisa gradien, gambar, dll.).
 *          - `data:ldbar/res,bubble(...)`: Ini menggunakan fungsi `bubble` bawaan dari `loading.io`
 *            untuk membuat efek gelembung.  Parameter di dalamnya adalah warna dan ukuran gelembung.
 *      - `data-stroke`: Warna garis tepi.
 *      - `data-stroke-width`: Ketebalan garis tepi.
 *  3.  JavaScript (di file script.js) akan menginisialisasi dan mengontrol
 *      loading bar ini, mengubah `data-value` secara dinamis untuk
 *      menunjukkan progress.
 */
#loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* --- Animasi Kartun --- */
#cartoon-walk {
    position: absolute;
    bottom: 0;
    right: -20vw; /* Mulai dari luar layar kanan (20% lebar viewport) */
    animation: walkAcross 3s linear forwards; /* Lebih cepat! */
    z-index: 10000;
}

#cartoon-walk img {
     width: 20vw;  /* Ukuran dasar untuk desktop */
    height: auto;
}

@keyframes walkAcross {
    from {
        transform: translateX(0vw); /* Mulai dari kanan (100% lebar viewport) */
    }
    to {
        transform: translateX(-1600%); /* Bergerak ke kiri layar */
    }
}

/* --- Hero Section --- */
#hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 50px 20px;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 60vh;
}

.hero-content {
    flex: 1;
    margin-right: 5rem;
}
.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.animated-text {
    font-size: 1.6rem;
    margin-bottom: 25px;
    opacity: 0;
    animation: fadeIn 2s forwards;
}

.cta-button {
    display: inline-block;
    background-color: var(--primary-color);
    color: #fff;
    padding: 12px 25px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

.cta-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 50%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Bagian About */
#about {
    background-color: #fff;
    padding: 40px 20px;
    text-align: center;
    max-width: 1200px;
    margin: auto;
    border-radius: 15px;
    margin-bottom: 0;
}

#about h2 {
    font-size: 2.5rem;
    margin-bottom: 0;
    color: var(--primary-color);
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;

}

.about-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    text-align: left;
}

.about-text {
    flex: 1.5;
    text-align: justify;
}

.about-text p {
    margin-bottom: 1rem;
}

.about-image {
    flex: 1;
}

.about-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
}

/* Bagian Skills */
#skills {
    padding: 40px 20px;
    text-align: center;
    max-width: 1200px;
    margin: auto;
}

#skills h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    color: var(--primary-color);
}

.animate-heading {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.skill {
    background-color: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
    opacity: 0;
    transform: scale(1);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
    overflow: hidden;
}

.skill:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.skill i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.skill h3 {
    font-size: 1.72rem;
    margin-bottom: 10px;
    white-space: normal;
    line-height: 1.2;
}

.skill p {
    font-size: 1rem;
}

/* Bagian Projects */
#projects {
    background-color: #fff;
    padding: 40px 20px;
    text-align: center;
    max-width: 1200px;
    margin: auto;
    border-radius: 25px;
}

#projects h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    color: var(--primary-color);
}

.projects-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.project {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.project:active .project-image {
    transform: scale(1.05);
}

.project-image {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
     order: -1;
}

.project-info {
   padding: 15px;
    text-align: center;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.project h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
     white-space: normal;
    line-height: 1.2;
}

.project p {
    font-size: 1rem;
    margin-bottom: 20px;
    padding: 0;
    text-align: center;

}

.project a {
    display: inline-block;
    background-color: #fff;
    color: var(--primary-color);
    padding: 8px 15px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s ease, color 0.3s ease;
    margin-top: auto;
    align-self: center;
}

.project a:hover {
    background-color: var(--primary-color);
    color: #fff;
}

/* Bagian Contact */
#contact {
    padding: 40px 20px;
    text-align: center;
    max-width: 1200px;
    margin: 0 auto;
}

#contact h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

#contact p {
    margin-bottom: 30px;
}

.contact-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.contact-links a {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    padding: 10px;
    transition: color 0.3s ease;
}

.contact-links a:hover {
    color: var(--secondary-color);
}

.contact-links i {
    font-size: 1.5rem;
    margin-right: 8px;
}

/* Footer */
footer {
    background-color: var(--primary-color);
    color: #fff;
    text-align: center;
    padding: 20px 0;
}

/* Media Queries untuk Responsivitas */

/* Tablet */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        flex-direction: column;
        width: 100%;
        position: absolute;
        top: 68px;
        left: 0;
        background-color: #fff;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    }

    .nav-links.show {
        display: flex;

    }


    .nav-links li {
        margin: 0;
        text-align: center;

    }

      .nav-links a {
        padding: 15px;
        border-bottom: 1px solid var(--light-gray);

    }

    .hamburger-menu {
        display: block;
        margin-right: 0.9rem;
    }

    /* Hero Section */
     #hero {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    /* Perubahan di sini: Pusatkan konten hero */
    .hero-content {
        margin-bottom: 30px;
        margin-right: 0; /* Hapus margin kanan */
        text-align: center; /* Pusatkan teks */
        display: flex;
        flex-direction: column;
        align-items: center;
    }
     .hero-content h1, .hero-content p {
        text-align: center; /* Pusatkan teks */
    }


    .hero-content h1 {
        font-size: 2.5rem;

    }

    .animated-text {
        font-size: 1.2rem;

    }
     .cta-button {
        display: flex; /*  Flexbox untuk centering */
        justify-content: center; /* Pusatkan secara horizontal */
    }
    /* --- */

    .about-container {
        flex-direction: column;
    }

    /* Project section */
    .project-info {
        position: static;
        opacity: 1;
        background: none;
        color: var(--text-color);
        transform: none;
        padding: 10px;
        text-align: center;
        justify-content: center;
    }

    .project a {
      display: initial;
    }

    .contact-links {
        flex-direction: row;
        justify-content: center;
        gap: 10px;
    }

      .contact-links a {
        padding: 5px;
    }
     /* --- Animasi Kartun (Mobile) --- */
    #cartoon-walk img {
        width: 50vw; /* Perbesar ukuran kartun */
    }

     /* Ubah animasi agar mulai dari luar kanan layar */
     @keyframes walkAcross {
        from {
            transform: translateX(20vw); /* Mulai dari kanan */
        }
        to {
            transform: translateX(-800%); /* Ke kiri */
        }
    }
}

/* Mobile (lebih kecil) */
@media (max-width: 480px) {
     #cartoon-walk img {
        width: 50vw;
    }
    .hero-content h1 {
        font-size: 2rem;
    }
}

/* Animasi */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Perubahan animasi: Terapkan pada elemen yang relevan */
.skill.animate-skill,
.animate-heading{
    opacity: 1;
    transform: translateY(0);
    transition: all 0.6s ease;
}

/* Tambahan: jika di-scroll di luar viewport, kembalikan ke state awal */
.skill.animate-skill.animate-out,
.animate-heading.animate-out {
    opacity: 0;
    transform: translateY(20px);
}