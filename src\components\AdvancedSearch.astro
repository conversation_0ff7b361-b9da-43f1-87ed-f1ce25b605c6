---
// Advanced Search and Filter Component
---

<div id="advanced-search" class="w-full max-w-4xl mx-auto mb-8">
  <!-- Search Header -->
  <div class="bg-gray-900/80 backdrop-blur-sm border border-cyan-500/30 rounded-t-lg p-4">
    <div class="flex items-center justify-between">
      <h3 class="text-cyan-400 font-mono text-lg font-semibold">Advanced Search</h3>
      <button 
        id="search-toggle" 
        class="text-gray-400 hover:text-cyan-400 transition-colors"
        aria-label="Toggle search filters"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Search Input -->
  <div class="bg-gray-800/80 backdrop-blur-sm border-x border-cyan-500/30 p-4">
    <div class="relative">
      <input
        type="text"
        id="search-input"
        placeholder="Search projects, skills, technologies..."
        class="w-full bg-gray-900/50 border border-gray-600 rounded-lg px-4 py-3 pl-12 text-white placeholder-gray-400 focus:border-cyan-500 focus:ring-2 focus:ring-cyan-500/20 transition-all"
        autocomplete="off"
      />
      <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      <button 
        id="clear-search" 
        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-cyan-400 transition-colors opacity-0 pointer-events-none"
        aria-label="Clear search"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Advanced Filters -->
  <div id="search-filters" class="bg-gray-800/80 backdrop-blur-sm border border-cyan-500/30 rounded-b-lg p-4 max-h-0 overflow-hidden transition-all duration-300">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- Category Filter -->
      <div>
        <label class="block text-sm font-mono text-gray-300 mb-2">Category</label>
        <select id="category-filter" class="w-full bg-gray-900/50 border border-gray-600 rounded px-3 py-2 text-white focus:border-cyan-500 transition-colors">
          <option value="">All Categories</option>
          <option value="projects">Projects</option>
          <option value="skills">Skills</option>
          <option value="experience">Experience</option>
          <option value="education">Education</option>
        </select>
      </div>

      <!-- Technology Filter -->
      <div>
        <label class="block text-sm font-mono text-gray-300 mb-2">Technology</label>
        <select id="tech-filter" class="w-full bg-gray-900/50 border border-gray-600 rounded px-3 py-2 text-white focus:border-cyan-500 transition-colors">
          <option value="">All Technologies</option>
          <option value="javascript">JavaScript</option>
          <option value="typescript">TypeScript</option>
          <option value="react">React</option>
          <option value="astro">Astro</option>
          <option value="tailwind">TailwindCSS</option>
          <option value="threejs">Three.js</option>
          <option value="gsap">GSAP</option>
        </select>
      </div>

      <!-- Difficulty Filter -->
      <div>
        <label class="block text-sm font-mono text-gray-300 mb-2">Difficulty</label>
        <select id="difficulty-filter" class="w-full bg-gray-900/50 border border-gray-600 rounded px-3 py-2 text-white focus:border-cyan-500 transition-colors">
          <option value="">All Levels</option>
          <option value="beginner">Beginner</option>
          <option value="intermediate">Intermediate</option>
          <option value="advanced">Advanced</option>
        </select>
      </div>
    </div>

    <!-- Sort Options -->
    <div class="mt-4 pt-4 border-t border-gray-700">
      <div class="flex flex-wrap items-center gap-4">
        <label class="text-sm font-mono text-gray-300">Sort by:</label>
        <div class="flex gap-2">
          <button class="sort-btn active" data-sort="relevance">Relevance</button>
          <button class="sort-btn" data-sort="date">Date</button>
          <button class="sort-btn" data-sort="name">Name</button>
          <button class="sort-btn" data-sort="difficulty">Difficulty</button>
        </div>
        <div class="flex gap-2 ml-auto">
          <button id="sort-order" class="text-sm font-mono text-cyan-400 hover:text-cyan-300 transition-colors" data-order="desc">
            ↓ Desc
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Filters -->
    <div class="mt-4 pt-4 border-t border-gray-700">
      <label class="block text-sm font-mono text-gray-300 mb-2">Quick Filters</label>
      <div class="flex flex-wrap gap-2">
        <button class="quick-filter-btn" data-filter="featured">Featured</button>
        <button class="quick-filter-btn" data-filter="recent">Recent</button>
        <button class="quick-filter-btn" data-filter="popular">Popular</button>
        <button class="quick-filter-btn" data-filter="web-dev">Web Dev</button>
        <button class="quick-filter-btn" data-filter="design">Design</button>
        <button class="quick-filter-btn" data-filter="data-analysis">Data Analysis</button>
      </div>
    </div>
  </div>

  <!-- Search Results Info -->
  <div id="search-results-info" class="mt-4 text-sm font-mono text-gray-400 text-center opacity-0 transition-opacity">
    <span id="results-count">0</span> results found
    <span id="search-time" class="ml-2"></span>
  </div>
</div>

<script>
  class AdvancedSearch {
    constructor() {
      this.isExpanded = false;
      this.searchData = [];
      this.filteredResults = [];
      this.currentSort = 'relevance';
      this.currentOrder = 'desc';
      this.activeFilters = new Set();
      
      this.init();
    }

    init() {
      this.setupEventListeners();
      this.loadSearchData();
      this.setupSearchIndex();
    }

    setupEventListeners() {
      const searchToggle = document.getElementById('search-toggle');
      const searchInput = document.getElementById('search-input');
      const clearSearch = document.getElementById('clear-search');
      const categoryFilter = document.getElementById('category-filter');
      const techFilter = document.getElementById('tech-filter');
      const difficultyFilter = document.getElementById('difficulty-filter');
      const sortOrder = document.getElementById('sort-order');

      searchToggle?.addEventListener('click', () => this.toggleFilters());
      searchInput?.addEventListener('input', (e) => this.handleSearch(e.target.value));
      clearSearch?.addEventListener('click', () => this.clearSearch());
      
      [categoryFilter, techFilter, difficultyFilter].forEach(filter => {
        filter?.addEventListener('change', () => this.applyFilters());
      });

      // Sort buttons
      document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.addEventListener('click', (e) => this.handleSort(e.target.dataset.sort));
      });

      sortOrder?.addEventListener('click', () => this.toggleSortOrder());

      // Quick filter buttons
      document.querySelectorAll('.quick-filter-btn').forEach(btn => {
        btn.addEventListener('click', (e) => this.toggleQuickFilter(e.target.dataset.filter));
      });

      // Real-time search
      searchInput?.addEventListener('input', () => {
        const hasValue = searchInput.value.length > 0;
        clearSearch.style.opacity = hasValue ? '1' : '0';
        clearSearch.style.pointerEvents = hasValue ? 'auto' : 'none';
      });
    }

    toggleFilters() {
      const filters = document.getElementById('search-filters');
      const toggle = document.getElementById('search-toggle');
      
      this.isExpanded = !this.isExpanded;
      
      if (this.isExpanded) {
        filters.style.maxHeight = '400px';
        filters.style.padding = '1rem';
        toggle.style.transform = 'rotate(180deg)';
      } else {
        filters.style.maxHeight = '0';
        filters.style.padding = '0 1rem';
        toggle.style.transform = 'rotate(0deg)';
      }
    }

    async loadSearchData() {
      // Simulate loading search data
      this.searchData = [
        {
          id: 1,
          title: "Cyberpunk Portfolio Website",
          category: "projects",
          technologies: ["astro", "typescript", "tailwind", "threejs", "gsap"],
          difficulty: "advanced",
          description: "Modern portfolio with 3D graphics and animations",
          date: "2024-01-15",
          featured: true,
          popular: true
        },
        {
          id: 2,
          title: "JavaScript Programming",
          category: "skills",
          technologies: ["javascript"],
          difficulty: "advanced",
          description: "Advanced JavaScript programming skills",
          date: "2023-06-01",
          featured: true
        },
        {
          id: 3,
          title: "React Development",
          category: "skills",
          technologies: ["react", "javascript"],
          difficulty: "intermediate",
          description: "React component development and state management",
          date: "2023-08-15",
          popular: true
        },
        {
          id: 4,
          title: "Data Analysis Dashboard",
          category: "projects",
          technologies: ["javascript", "react"],
          difficulty: "intermediate",
          description: "Interactive data visualization dashboard",
          date: "2023-12-01",
          recent: true
        }
      ];
      
      this.filteredResults = [...this.searchData];
    }

    setupSearchIndex() {
      // Create search index for better performance
      this.searchIndex = this.searchData.map(item => ({
        id: item.id,
        searchText: [
          item.title,
          item.description,
          item.category,
          ...item.technologies
        ].join(' ').toLowerCase()
      }));
    }

    handleSearch(query) {
      const startTime = performance.now();
      
      if (!query.trim()) {
        this.filteredResults = [...this.searchData];
      } else {
        const searchTerm = query.toLowerCase();
        const matchingIds = this.searchIndex
          .filter(item => item.searchText.includes(searchTerm))
          .map(item => item.id);
        
        this.filteredResults = this.searchData.filter(item => 
          matchingIds.includes(item.id)
        );
      }
      
      this.applyFilters();
      this.updateResultsInfo(performance.now() - startTime);
      this.displayResults();
    }

    applyFilters() {
      let results = [...this.filteredResults];
      
      // Apply dropdown filters
      const category = document.getElementById('category-filter')?.value;
      const tech = document.getElementById('tech-filter')?.value;
      const difficulty = document.getElementById('difficulty-filter')?.value;
      
      if (category) {
        results = results.filter(item => item.category === category);
      }
      
      if (tech) {
        results = results.filter(item => item.technologies.includes(tech));
      }
      
      if (difficulty) {
        results = results.filter(item => item.difficulty === difficulty);
      }
      
      // Apply quick filters
      this.activeFilters.forEach(filter => {
        results = results.filter(item => item[filter] === true);
      });
      
      this.filteredResults = results;
      this.sortResults();
    }

    handleSort(sortType) {
      this.currentSort = sortType;
      
      // Update active sort button
      document.querySelectorAll('.sort-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-sort="${sortType}"]`)?.classList.add('active');
      
      this.sortResults();
    }

    sortResults() {
      this.filteredResults.sort((a, b) => {
        let comparison = 0;
        
        switch (this.currentSort) {
          case 'date':
            comparison = new Date(a.date) - new Date(b.date);
            break;
          case 'name':
            comparison = a.title.localeCompare(b.title);
            break;
          case 'difficulty':
            const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
            comparison = difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
            break;
          default: // relevance
            comparison = 0;
        }
        
        return this.currentOrder === 'desc' ? -comparison : comparison;
      });
      
      this.displayResults();
    }

    toggleSortOrder() {
      this.currentOrder = this.currentOrder === 'desc' ? 'asc' : 'desc';
      const btn = document.getElementById('sort-order');
      btn.textContent = this.currentOrder === 'desc' ? '↓ Desc' : '↑ Asc';
      btn.dataset.order = this.currentOrder;
      
      this.sortResults();
    }

    toggleQuickFilter(filter) {
      const btn = document.querySelector(`[data-filter="${filter}"]`);
      
      if (this.activeFilters.has(filter)) {
        this.activeFilters.delete(filter);
        btn.classList.remove('active');
      } else {
        this.activeFilters.add(filter);
        btn.classList.add('active');
      }
      
      this.applyFilters();
    }

    clearSearch() {
      const searchInput = document.getElementById('search-input');
      const clearBtn = document.getElementById('clear-search');
      
      searchInput.value = '';
      clearBtn.style.opacity = '0';
      clearBtn.style.pointerEvents = 'none';
      
      this.filteredResults = [...this.searchData];
      this.displayResults();
      this.updateResultsInfo(0);
    }

    updateResultsInfo(searchTime) {
      const resultsInfo = document.getElementById('search-results-info');
      const resultsCount = document.getElementById('results-count');
      const searchTimeEl = document.getElementById('search-time');
      
      resultsCount.textContent = this.filteredResults.length;
      searchTimeEl.textContent = `(${searchTime.toFixed(2)}ms)`;
      
      resultsInfo.style.opacity = '1';
    }

    displayResults() {
      // Emit custom event with filtered results
      const event = new CustomEvent('searchResults', {
        detail: {
          results: this.filteredResults,
          total: this.searchData.length,
          query: document.getElementById('search-input')?.value || ''
        }
      });
      
      document.dispatchEvent(event);
    }
  }

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.advancedSearch = new AdvancedSearch();
  });
</script>

<style>
  .sort-btn {
    @apply px-3 py-1 text-xs font-mono bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors;
  }
  
  .sort-btn.active {
    @apply bg-cyan-500/20 text-cyan-400 border border-cyan-500/30;
  }
  
  .quick-filter-btn {
    @apply px-3 py-1 text-xs font-mono bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors;
  }
  
  .quick-filter-btn.active {
    @apply bg-purple-500/20 text-purple-400 border border-purple-500/30;
  }
  
  #search-toggle {
    transition: transform 0.3s ease;
  }
</style>
