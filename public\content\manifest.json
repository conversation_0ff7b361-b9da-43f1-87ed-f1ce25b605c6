{"version": "1.0.0", "lastUpdated": "2024-01-15T10:00:00Z", "types": [{"id": "projects", "name": "Projects", "description": "Portfolio projects and case studies", "schema": {"id": "string", "title": "string", "description": "string", "content": "markdown", "image": "string", "gallery": "array", "technologies": "array", "category": "string", "status": "string", "date": "date", "url": "string", "github": "string", "featured": "boolean", "tags": "array"}, "count": 8, "lastModified": "2024-01-15T09:30:00Z"}, {"id": "blog", "name": "Blog Posts", "description": "Technical articles and insights", "schema": {"id": "string", "title": "string", "description": "string", "content": "markdown", "author": "string", "date": "date", "publishedDate": "date", "modifiedDate": "date", "category": "string", "tags": "array", "featured": "boolean", "image": "string", "readTime": "number", "status": "string"}, "count": 12, "lastModified": "2024-01-14T16:45:00Z"}, {"id": "skills", "name": "Skills", "description": "Technical skills and competencies", "schema": {"id": "string", "name": "string", "category": "string", "level": "number", "icon": "string", "description": "string", "experience": "string", "certifications": "array", "projects": "array", "lastUsed": "date"}, "count": 20, "lastModified": "2024-01-15T08:15:00Z"}, {"id": "experience", "name": "Work Experience", "description": "Professional work history", "schema": {"id": "string", "company": "string", "position": "string", "description": "string", "startDate": "date", "endDate": "date", "current": "boolean", "location": "string", "type": "string", "technologies": "array", "achievements": "array", "logo": "string"}, "count": 5, "lastModified": "2024-01-10T14:20:00Z"}, {"id": "education", "name": "Education", "description": "Educational background and certifications", "schema": {"id": "string", "institution": "string", "degree": "string", "field": "string", "startDate": "date", "endDate": "date", "current": "boolean", "gpa": "number", "description": "string", "achievements": "array", "logo": "string"}, "count": 3, "lastModified": "2024-01-08T11:30:00Z"}, {"id": "testimonials", "name": "Testimonials", "description": "Client and colleague testimonials", "schema": {"id": "string", "name": "string", "position": "string", "company": "string", "content": "string", "rating": "number", "date": "date", "avatar": "string", "featured": "boolean", "project": "string"}, "count": 6, "lastModified": "2024-01-12T13:45:00Z"}], "categories": {"projects": ["Web Development", "Mobile App", "Data Analysis", "UI/UX Design", "API Development", "DevOps"], "blog": ["Tutorial", "Case Study", "Opinion", "News", "Review", "Guide"], "skills": ["frontend", "backend", "database", "tools", "design", "finance", "soft-skills"], "experience": ["Full-time", "Part-time", "Contract", "Freelance", "Internship"]}, "tags": ["JavaScript", "TypeScript", "React", "Astro", "Node.js", "Python", "Three.js", "GSAP", "TailwindCSS", "MongoDB", "PostgreSQL", "Git", "<PERSON>er", "AWS", "Figma", "Photoshop", "Excel", "Accounting", "Finance", "Portfolio", "Web Design", "Animation", "Performance", "Accessibility", "SEO", "PWA", "Responsive", "Mobile-First", "Cross-Browser"], "sources": {"local": {"enabled": true, "path": "/content", "priority": 3}, "api": {"enabled": false, "endpoint": "/api/content", "priority": 2}, "cms": {"enabled": false, "endpoint": null, "priority": 1}}, "cache": {"enabled": true, "ttl": 3600, "maxSize": 100}, "features": {"versioning": true, "realTimeUpdates": false, "search": true, "filtering": true, "sorting": true, "pagination": true, "preloading": true}, "metadata": {"generator": "Astro Content Manager", "generatedAt": "2024-01-15T10:00:00Z", "environment": "production"}}