---
// Theme Customizer Component
---

<div id="theme-customizer" class="theme-customizer">
  <button
    id="theme-toggle"
    class="theme-toggle-btn"
    aria-label="Toggle theme customizer"
    title="Customize Theme"
  >
    <svg class="theme-icon" width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </button>

  <div id="theme-panel" class="theme-panel">
    <div class="theme-panel-header">
      <h3>Theme Customizer</h3>
      <button id="close-panel" class="close-btn" aria-label="Close theme panel">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path
            d="M18 6L6 18M6 6L18 18"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <div class="theme-options">
      <!-- Color Scheme -->
      <div class="option-group">
        <label class="option-label">Color Scheme</label>
        <div class="color-schemes">
          <button class="color-scheme active" data-scheme="cyberpunk" title="Cyberpunk">
            <div class="scheme-preview">
              <div class="color-dot" style="background: #00ffff;"></div>
              <div class="color-dot" style="background: #ff00ff;"></div>
              <div class="color-dot" style="background: #00ff00;"></div>
            </div>
            <span>Cyberpunk</span>
          </button>
          
          <button class="color-scheme" data-scheme="matrix" title="Matrix">
            <div class="scheme-preview">
              <div class="color-dot" style="background: #00ff00;"></div>
              <div class="color-dot" style="background: #008000;"></div>
              <div class="color-dot" style="background: #90ee90;"></div>
            </div>
            <span>Matrix</span>
          </button>
          
          <button class="color-scheme" data-scheme="neon" title="Neon">
            <div class="scheme-preview">
              <div class="color-dot" style="background: #ff6600;"></div>
              <div class="color-dot" style="background: #ff0080;"></div>
              <div class="color-dot" style="background: #8000ff;"></div>
            </div>
            <span>Neon</span>
          </button>
        </div>
      </div>

      <!-- Background Mode -->
      <div class="option-group">
        <label class="option-label">Background</label>
        <div class="toggle-group">
          <button class="toggle-btn active" data-bg="particles">Particles</button>
          <button class="toggle-btn" data-bg="matrix">Matrix</button>
          <button class="toggle-btn" data-bg="minimal">Minimal</button>
        </div>
      </div>

      <!-- Animation Speed -->
      <div class="option-group">
        <label class="option-label">Animation Speed</label>
        <div class="slider-container">
          <input
            type="range"
            id="animation-speed"
            min="0.5"
            max="2"
            step="0.1"
            value="1"
            class="slider"
          />
          <span class="slider-value">1x</span>
        </div>
      </div>

      <!-- Particle Count -->
      <div class="option-group">
        <label class="option-label">Particle Density</label>
        <div class="slider-container">
          <input
            type="range"
            id="particle-count"
            min="500"
            max="2000"
            step="100"
            value="1000"
            class="slider"
          />
          <span class="slider-value">1000</span>
        </div>
      </div>

      <!-- Reset Button -->
      <div class="option-group">
        <button id="reset-theme" class="reset-btn">Reset to Default</button>
      </div>
    </div>
  </div>
</div>

<style>
  .theme-customizer {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    z-index: 1000;
  }

  .theme-toggle-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(0, 255, 255, 0.5);
    color: #00ffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
  }

  .theme-toggle-btn:hover {
    border-color: #00ffff;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .theme-panel {
    position: absolute;
    right: 60px;
    top: 0;
    width: 300px;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(20px);
    transform: translateX(100%) scale(0.8);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .theme-panel.active {
    transform: translateX(0) scale(1);
    opacity: 1;
    visibility: visible;
  }

  .theme-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  }

  .theme-panel-header h3 {
    color: #00ffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: color 0.3s ease;
  }

  .close-btn:hover {
    color: #00ffff;
  }

  .option-group {
    margin-bottom: 20px;
  }

  .option-label {
    display: block;
    color: #ccc;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .color-schemes {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .color-scheme {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 80px;
  }

  .color-scheme.active {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
  }

  .scheme-preview {
    display: flex;
    gap: 4px;
    margin-bottom: 5px;
    justify-content: center;
  }

  .color-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .color-scheme span {
    display: block;
    color: #ccc;
    font-size: 12px;
    text-align: center;
  }

  .toggle-group {
    display: flex;
    gap: 5px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 4px;
  }

  .toggle-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: #888;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
  }

  .toggle-btn.active {
    background: #00ffff;
    color: #000;
  }

  .slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .slider {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
  }

  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #00ffff;
    border-radius: 50%;
    cursor: pointer;
  }

  .slider-value {
    color: #00ffff;
    font-size: 12px;
    font-weight: 600;
    min-width: 30px;
  }

  .reset-btn {
    width: 100%;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    color: #ff6b6b;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .reset-btn:hover {
    background: rgba(255, 0, 0, 0.2);
    border-color: #ff6b6b;
  }

  @media (max-width: 768px) {
    .theme-customizer {
      right: 10px;
    }
    
    .theme-panel {
      width: 280px;
      right: 50px;
    }
  }
</style>

<script>
  class ThemeCustomizer {
    constructor() {
      this.panel = document.getElementById('theme-panel');
      this.toggleBtn = document.getElementById('theme-toggle');
      this.closeBtn = document.getElementById('close-panel');
      this.resetBtn = document.getElementById('reset-theme');
      
      this.currentTheme = this.loadTheme();
      this.init();
    }

    init() {
      this.setupEventListeners();
      this.applyTheme(this.currentTheme);
    }

    setupEventListeners() {
      this.toggleBtn?.addEventListener('click', () => this.togglePanel());
      this.closeBtn?.addEventListener('click', () => this.closePanel());
      this.resetBtn?.addEventListener('click', () => this.resetTheme());

      // Color scheme buttons
      document.querySelectorAll('.color-scheme').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const scheme = e.currentTarget.dataset.scheme;
          this.setColorScheme(scheme);
        });
      });

      // Background toggle
      document.querySelectorAll('[data-bg]').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const bg = e.currentTarget.dataset.bg;
          this.setBackground(bg);
        });
      });

      // Sliders
      const animationSlider = document.getElementById('animation-speed');
      const particleSlider = document.getElementById('particle-count');

      animationSlider?.addEventListener('input', (e) => {
        this.setAnimationSpeed(e.target.value);
      });

      particleSlider?.addEventListener('input', (e) => {
        this.setParticleCount(e.target.value);
      });
    }

    togglePanel() {
      this.panel?.classList.toggle('active');
    }

    closePanel() {
      this.panel?.classList.remove('active');
    }

    setColorScheme(scheme) {
      document.querySelectorAll('.color-scheme').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-scheme="${scheme}"]`)?.classList.add('active');
      
      this.currentTheme.colorScheme = scheme;
      this.applyColorScheme(scheme);
      this.saveTheme();
    }

    setBackground(bg) {
      document.querySelectorAll('[data-bg]').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-bg="${bg}"]`)?.classList.add('active');
      
      this.currentTheme.background = bg;
      this.applyBackground(bg);
      this.saveTheme();
    }

    setAnimationSpeed(speed) {
      document.querySelector('.slider-value').textContent = speed + 'x';
      this.currentTheme.animationSpeed = speed;
      this.applyAnimationSpeed(speed);
      this.saveTheme();
    }

    setParticleCount(count) {
      document.querySelectorAll('.slider-value')[1].textContent = count;
      this.currentTheme.particleCount = count;
      this.applyParticleCount(count);
      this.saveTheme();
    }

    applyTheme(theme) {
      this.applyColorScheme(theme.colorScheme);
      this.applyBackground(theme.background);
      this.applyAnimationSpeed(theme.animationSpeed);
      this.applyParticleCount(theme.particleCount);
    }

    applyColorScheme(scheme) {
      document.documentElement.setAttribute('data-theme', scheme);
    }

    applyBackground(bg) {
      document.documentElement.setAttribute('data-background', bg);
    }

    applyAnimationSpeed(speed) {
      document.documentElement.style.setProperty('--animation-speed', speed);
    }

    applyParticleCount(count) {
      // Communicate with particle system
      if (window.particleSystem) {
        window.particleSystem.updateParticleCount(count);
      }
    }

    resetTheme() {
      this.currentTheme = {
        colorScheme: 'cyberpunk',
        background: 'particles',
        animationSpeed: 1,
        particleCount: 1000
      };
      
      this.applyTheme(this.currentTheme);
      this.saveTheme();
      
      // Update UI
      document.querySelector('[data-scheme="cyberpunk"]')?.classList.add('active');
      document.querySelector('[data-bg="particles"]')?.classList.add('active');
      document.getElementById('animation-speed').value = 1;
      document.getElementById('particle-count').value = 1000;
    }

    saveTheme() {
      localStorage.setItem('portfolio-theme', JSON.stringify(this.currentTheme));
    }

    loadTheme() {
      const saved = localStorage.getItem('portfolio-theme');
      return saved ? JSON.parse(saved) : {
        colorScheme: 'cyberpunk',
        background: 'particles',
        animationSpeed: 1,
        particleCount: 1000
      };
    }
  }

  // Initialize when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ThemeCustomizer();
  });
</script>
