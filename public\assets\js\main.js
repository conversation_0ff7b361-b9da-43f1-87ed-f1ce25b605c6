/**
 * Main JavaScript file for portfolio website
 * Handles core functionality, performance monitoring, and user interactions
 */

// Performance monitoring
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.observers = [];
    this.init();
  }

  init() {
    // Mark page load start
    this.mark('page-load-start');
    
    // Initialize observers
    this.initCoreWebVitals();
    this.initResourceTiming();
    
    // Track page visibility
    this.trackPageVisibility();
    
    // Track user interactions
    this.trackUserInteractions();
  }

  mark(name) {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  }

  measure(name, startMark, endMark) {
    if ('performance' in window && 'measure' in performance) {
      try {
        performance.measure(name, startMark, endMark);
        const measure = performance.getEntriesByName(name, 'measure')[0];
        this.metrics[name] = measure.duration;
        return measure.duration;
      } catch (e) {
        console.warn('Performance measure failed:', e);
      }
    }
  }

  initCoreWebVitals() {
    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.lcp = lastEntry.startTime;
      });
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // First Input Delay
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.metrics.fid = entry.processingStart - entry.startTime;
        });
      });

      try {
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        this.metrics.cls = clsValue;
      });

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }
    }
  }

  initResourceTiming() {
    if ('PerformanceObserver' in window) {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.duration > 1000) { // Resources taking more than 1s
            console.warn(`Slow resource: ${entry.name} took ${entry.duration}ms`);
          }
        });
      });

      try {
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);
      } catch (e) {
        console.warn('Resource observer not supported');
      }
    }
  }

  trackPageVisibility() {
    let startTime = Date.now();
    
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        const sessionTime = Date.now() - startTime;
        this.metrics.sessionTime = sessionTime;
      } else {
        startTime = Date.now();
      }
    });
  }

  trackUserInteractions() {
    let clickCount = 0;
    let scrollDepth = 0;

    // Track clicks
    document.addEventListener('click', (e) => {
      clickCount++;
      this.metrics.clickCount = clickCount;
      
      // Track specific element clicks
      if (e.target.matches('a[href^="#"]')) {
        this.mark(`nav-click-${e.target.getAttribute('href')}`);
      }
    });

    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent;
        this.metrics.maxScrollDepth = maxScroll;
      }
    });
  }

  getMetrics() {
    return { ...this.metrics };
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Theme management
class ThemeManager {
  constructor() {
    this.currentTheme = localStorage.getItem('theme') || 'cyberpunk';
    this.init();
  }

  init() {
    this.applyTheme(this.currentTheme);
    this.setupThemeToggle();
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    this.currentTheme = theme;
    
    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('themechange', { 
      detail: { theme } 
    }));
  }

  setupThemeToggle() {
    const themeToggle = document.querySelector('[data-theme-toggle]');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => {
        this.cycleTheme();
      });
    }
  }

  cycleTheme() {
    const themes = ['cyberpunk', 'neon', 'matrix', 'minimal'];
    const currentIndex = themes.indexOf(this.currentTheme);
    const nextIndex = (currentIndex + 1) % themes.length;
    this.applyTheme(themes[nextIndex]);
  }
}

// Smooth scrolling for navigation
class SmoothScroll {
  constructor() {
    this.init();
  }

  init() {
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href^="#"]');
      if (link) {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          this.scrollToElement(targetElement);
        }
      }
    });
  }

  scrollToElement(element) {
    const headerHeight = document.querySelector('header')?.offsetHeight || 0;
    const targetPosition = element.offsetTop - headerHeight - 20;
    
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }
}

// Initialize everything when DOM is ready
class App {
  constructor() {
    this.performanceMonitor = null;
    this.themeManager = null;
    this.smoothScroll = null;
    this.init();
  }

  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    // Initialize core systems
    this.performanceMonitor = new PerformanceMonitor();
    this.themeManager = new ThemeManager();
    this.smoothScroll = new SmoothScroll();
    
    // Mark setup complete
    this.performanceMonitor.mark('app-setup-complete');
    
    // Add loaded class to body
    document.body.classList.add('loaded');
    
    // Setup error handling
    this.setupErrorHandling();
    
    // Setup intersection observers for animations
    this.setupIntersectionObservers();
    
    console.log('Portfolio app initialized successfully');
  }

  setupErrorHandling() {
    window.addEventListener('error', (e) => {
      console.error('JavaScript error:', e.error);
    });

    window.addEventListener('unhandledrejection', (e) => {
      console.error('Unhandled promise rejection:', e.reason);
    });
  }

  setupIntersectionObservers() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe elements with animation classes
    document.querySelectorAll('[data-animate]').forEach((el) => {
      observer.observe(el);
    });
  }
}

// Global app instance
window.portfolioApp = new App();

// Expose performance monitor globally for debugging
window.performanceMonitor = window.portfolioApp.performanceMonitor;
