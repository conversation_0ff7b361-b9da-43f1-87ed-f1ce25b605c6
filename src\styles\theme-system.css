/* Advanced Theme System Styles */

/* CSS Custom Properties (CSS Variables) */
:root {
  /* Default Cyberpunk Theme */
  --color-primary: #00ffff;
  --color-secondary: #ff00ff;
  --color-accent: #00ff00;
  --color-background: #0a0a0a;
  --color-surface: #1a1a2e;
  --color-text: #ffffff;
  --color-text-secondary: #cccccc;
  --color-border: #333333;
  --color-success: #00ff00;
  --color-warning: #ffff00;
  --color-error: #ff0000;

  /* Gradients */
  --gradient-primary: linear-gradient(45deg, #00ffff, #ff00ff);
  --gradient-secondary: linear-gradient(135deg, #0a0a0a, #1a1a2e, #16213e);
  --gradient-accent: linear-gradient(90deg, #00ff00, #00ffff);

  /* Shadows */
  --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.5);
  --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
  --shadow-text: 0 0 10px rgba(0, 255, 255, 0.8);

  /* Animations */
  --animation-duration: 0.3s;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-glitch: 1;
  --animation-particles: 1;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;
}

/* Theme-specific body classes */
body.theme-cyberpunk {
  background: var(--gradient-secondary);
  color: var(--color-text);
}

body.theme-neon {
  background: linear-gradient(135deg, #000000, #1a0033, #330066);
  color: var(--color-text);
}

body.theme-matrix {
  background: linear-gradient(135deg, #000000, #001100, #002200);
  color: var(--color-text);
}

body.theme-minimal {
  background: linear-gradient(135deg, #1e1e1e, #2d2d2d, #3c3c3c);
  color: var(--color-text);
}

/* Theme Switcher Modal */
.theme-switcher-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn var(--animation-duration) var(--animation-easing);
}

.theme-switcher-content {
  background: var(--color-surface);
  border: 2px solid var(--color-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  max-width: 500px;
  width: 90%;
  position: relative;
  box-shadow: var(--shadow-glow);
  animation: slideUp var(--animation-duration) var(--animation-easing);
}

.theme-switcher-content h2 {
  color: var(--color-primary);
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  text-shadow: var(--shadow-text);
}

.theme-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.theme-option {
  background: transparent;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

.theme-option:hover,
.theme-option:focus {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.theme-option.active {
  border-color: var(--color-primary);
  background: rgba(var(--color-primary), 0.1);
  box-shadow: var(--shadow-glow);
}

.theme-preview {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.theme-switcher-close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: transparent;
  border: none;
  color: var(--color-text);
  font-size: var(--font-size-xl);
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all var(--animation-duration) var(--animation-easing);
}

.theme-switcher-close:hover,
.theme-switcher-close:focus {
  background: var(--color-primary);
  color: var(--color-background);
}

/* Accessibility Enhancements */
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

.high-contrast {
  --color-text: #ffffff;
  --color-background: #000000;
  --color-border: #ffffff;
}

.high-contrast * {
  text-shadow: none !important;
  box-shadow: none !important;
}

/* Focus indicators for accessibility */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Theme-aware components */
.btn {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.btn:hover,
.btn:focus {
  background: var(--color-primary);
  color: var(--color-background);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.btn-secondary {
  border-color: var(--color-secondary);
  color: var(--color-secondary);
}

.btn-secondary:hover,
.btn-secondary:focus {
  background: var(--color-secondary);
  color: var(--color-background);
}

.card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  transition: all var(--animation-duration) var(--animation-easing);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-glow);
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-accent {
  color: var(--color-accent);
}

.bg-primary {
  background: var(--color-primary);
}

.bg-secondary {
  background: var(--color-secondary);
}

.bg-surface {
  background: var(--color-surface);
}

.gradient-primary {
  background: var(--gradient-primary);
}

.gradient-secondary {
  background: var(--gradient-secondary);
}

.gradient-accent {
  background: var(--gradient-accent);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: var(--shadow-glow);
  }
  50% {
    box-shadow: 0 0 30px var(--color-primary);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-switcher-content {
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
  }
  
  .theme-options {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .theme-options {
    grid-template-columns: 1fr;
  }
}

/* Print styles */
@media print {
  .theme-switcher-modal {
    display: none;
  }
  
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}
