{"ci": {"collect": {"url": ["http://localhost:4321/", "http://localhost:4321/projects"], "startServerCommand": "npm run preview", "startServerReadyPattern": "Local:.*:4321", "startServerReadyTimeout": 30000, "numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "emulatedFormFactor": "desktop", "locale": "en-US"}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.95}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["error", {"minScore": 0.95}], "categories:pwa": "off", "first-contentful-paint": ["error", {"maxNumericValue": 2000}], "largest-contentful-paint": ["error", {"maxNumericValue": 2500}], "first-meaningful-paint": ["error", {"maxNumericValue": 2000}], "speed-index": ["error", {"maxNumericValue": 3000}], "interactive": ["error", {"maxNumericValue": 3500}], "max-potential-fid": ["error", {"maxNumericValue": 100}], "cumulative-layout-shift": ["error", {"maxNumericValue": 0.1}], "total-blocking-time": ["error", {"maxNumericValue": 300}], "uses-webp-images": "error", "uses-optimized-images": "error", "modern-image-formats": "error", "uses-text-compression": "error", "uses-responsive-images": "error", "efficient-animated-content": "error", "unused-css-rules": "warn", "unused-javascript": "warn", "render-blocking-resources": "warn", "color-contrast": "error", "image-alt": "error", "label": "error", "link-name": "error", "button-name": "error", "document-title": "error", "html-has-lang": "error", "meta-description": "error", "valid-lang": "error", "aria-allowed-attr": "error", "aria-required-attr": "error", "aria-valid-attr-value": "error", "aria-valid-attr": "error", "heading-order": "error", "tabindex": "error", "focus-traps": "error", "focusable-controls": "error", "interactive-element-affordance": "error", "logical-tab-order": "error", "managed-focus": "error", "offscreen-content-hidden": "error", "use-landmarks": "error", "visual-order-follows-dom": "error", "is-on-https": "error", "uses-http2": "error", "no-vulnerable-libraries": "error", "csp-xss": "warn", "external-anchors-use-rel-noopener": "error", "viewport": "error", "without-javascript": "error", "canonical": "error", "robots-txt": "error", "hreflang": "off", "structured-data": "warn"}}, "upload": {"target": "temporary-public-storage", "reportFilenamePattern": "%%PATHNAME%%-%%DATETIME%%-report.%%EXTENSION%%"}, "server": {"port": 9001, "storage": {"storageMethod": "sql", "sqlDialect": "sqlite", "sqlDatabasePath": ".lighthouseci/database.sql"}}}}