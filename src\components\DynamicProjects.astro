---
import { getCollection } from 'astro:content';
import InteractiveCard from './InteractiveCard.astro';

// Get all projects, sorted by featured and date
const allProjects = await getCollection('projects');
const projects = allProjects
  .filter(project => project.data.status === 'completed')
  .sort((a, b) => {
    // Featured projects first
    if (a.data.featured && !b.data.featured) return -1;
    if (!a.data.featured && b.data.featured) return 1;
    // Then by end date (most recent first)
    return new Date(b.data.endDate || b.data.startDate).getTime() - 
           new Date(a.data.endDate || a.data.startDate).getTime();
  });

const featuredProjects = projects.filter(project => project.data.featured).slice(0, 3);
const otherProjects = projects.filter(project => !project.data.featured).slice(0, 6);

export interface Props {
  showAll?: boolean;
  limit?: number;
}

const { showAll = false, limit = 6 } = Astro.props;
const displayProjects = showAll ? projects : [...featuredProjects, ...otherProjects].slice(0, limit);
---

<div class="projects-grid">
  {displayProjects.map((project, index) => (
    <InteractiveCard 
      class={`project-card ${project.data.featured ? 'featured' : ''}`}
      glowColor={project.data.featured ? 'cyan' : 'pink'}
      hoverScale={1.03}
      rotateOnHover={true}
    >
      <article class="project-content">
        <!-- Project Image -->
        <div class="project-image-container">
          <img
            src={project.data.image}
            alt={project.data.title}
            class="project-image"
            loading="lazy"
          />
          <div class="project-overlay">
            <div class="project-status">
              <span class={`status-badge ${project.data.status}`}>
                {project.data.status.replace('-', ' ').toUpperCase()}
              </span>
              {project.data.featured && (
                <span class="featured-badge">FEATURED</span>
              )}
            </div>
          </div>
        </div>

        <!-- Project Info -->
        <div class="project-info">
          <div class="project-header">
            <h3 class="project-title">{project.data.title}</h3>
            <div class="project-category">
              <span class="category-badge">{project.data.category.toUpperCase()}</span>
            </div>
          </div>

          <p class="project-description">{project.data.description}</p>

          <!-- Technologies -->
          <div class="project-technologies">
            {project.data.technologies.slice(0, 4).map((tech) => (
              <span class="tech-tag">{tech}</span>
            ))}
            {project.data.technologies.length > 4 && (
              <span class="tech-more">+{project.data.technologies.length - 4}</span>
            )}
          </div>

          <!-- Project Links -->
          <div class="project-links">
            {project.data.liveUrl && (
              <a
                href={project.data.liveUrl}
                target="_blank"
                rel="noopener noreferrer"
                class="project-link live"
                aria-label={`View ${project.data.title} live demo`}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M18 13V19C18 19.5304 17.7893 20.0391 17.4142 20.4142C17.0391 20.7893 16.5304 21 16 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V8C3 7.46957 3.21071 6.96086 3.58579 6.58579C3.96086 6.21071 4.46957 6 5 6H11"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M15 3H21V9"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M10 14L21 3"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                Live Demo
              </a>
            )}
            
            {project.data.githubUrl && (
              <a
                href={project.data.githubUrl}
                target="_blank"
                rel="noopener noreferrer"
                class="project-link github"
                aria-label={`View ${project.data.title} source code`}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M9 19C4 20.5 4 16.5 2 16M22 16V19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H16C15.4696 21 14.9609 20.7893 14.5858 20.4142C14.2107 20.0391 14 19.5304 14 19V16.5C14 15.5 14.1 15.1 14.5 14.5C11.3 14.2 8 13 8 8.5C8 7.5 8.4 6.6 9 6C8.8 5.2 8.8 4.2 9 3.5C9 3.5 10.1 3.1 12 4.1C13.1 3.8 14.9 3.8 16 4.1C17.9 3.1 19 3.5 19 3.5C19.2 4.2 19.2 5.2 19 6C19.6 6.6 20 7.5 20 8.5C20 13 16.7 14.2 13.5 14.5C13.9 14.9 14 15.4 14 16V19"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                Source Code
              </a>
            )}

            <a
              href={`/projects/${project.slug}`}
              class="project-link details"
              aria-label={`View ${project.data.title} details`}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path
                  d="M13 2L3 14H12L11 22L21 10H12L13 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Details
            </a>
          </div>

          <!-- Project Timeline -->
          <div class="project-timeline">
            <time class="project-date">
              {new Date(project.data.startDate).toLocaleDateString('en-US', {
                month: 'short',
                year: 'numeric'
              })}
              {project.data.endDate && (
                ` - ${new Date(project.data.endDate).toLocaleDateString('en-US', {
                  month: 'short',
                  year: 'numeric'
                })}`
              )}
            </time>
          </div>
        </div>
      </article>
    </InteractiveCard>
  ))}
</div>

{!showAll && projects.length > limit && (
  <div class="projects-actions">
    <a href="/projects" class="view-all-btn">
      <span>View All Projects</span>
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
        <path
          d="M5 12H19M19 12L12 5M19 12L12 19"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </a>
  </div>
)}

<style>
  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .project-card {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .project-card.featured {
    border-color: rgba(0, 255, 255, 0.3);
    background: rgba(0, 255, 255, 0.05);
  }

  .project-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .project-image-container {
    position: relative;
    aspect-ratio: 16/9;
    overflow: hidden;
  }

  .project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .project-card:hover .project-image {
    transform: scale(1.05);
  }

  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.1),
      rgba(0, 0, 0, 0.7)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .project-card:hover .project-overlay {
    opacity: 1;
  }

  .project-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
  }

  .status-badge,
  .featured-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .status-badge.completed {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  .featured-badge {
    background: rgba(0, 255, 255, 0.2);
    color: #00ffff;
    border: 1px solid rgba(0, 255, 255, 0.3);
  }

  .project-info {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
  }

  .project-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    line-height: 1.3;
  }

  .category-badge {
    padding: 0.25rem 0.5rem;
    background: rgba(255, 0, 255, 0.2);
    color: #ff00ff;
    border: 1px solid rgba(255, 0, 255, 0.3);
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
  }

  .project-description {
    color: #d1d5db;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
  }

  .project-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tech-tag {
    padding: 0.25rem 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .tech-more {
    padding: 0.25rem 0.5rem;
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .project-links {
    display: flex;
    gap: 0.75rem;
    margin-top: auto;
  }

  .project-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid transparent;
  }

  .project-link.live {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border-color: rgba(34, 197, 94, 0.3);
  }

  .project-link.github {
    background: rgba(156, 163, 175, 0.1);
    color: #9ca3af;
    border-color: rgba(156, 163, 175, 0.3);
  }

  .project-link.details {
    background: rgba(0, 255, 255, 0.1);
    color: #00ffff;
    border-color: rgba(0, 255, 255, 0.3);
  }

  .project-link:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .project-timeline {
    margin-top: 0.5rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project-date {
    color: #9ca3af;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .projects-actions {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  .view-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(45deg, #00ffff, #ff00ff);
    color: #000;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 255, 255, 0.3);
  }

  @media (max-width: 768px) {
    .projects-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .project-header {
      flex-direction: column;
      gap: 0.5rem;
    }

    .project-links {
      flex-wrap: wrap;
    }
  }
</style>
