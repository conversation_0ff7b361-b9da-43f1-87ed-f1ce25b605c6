/* style-coming_soon.css */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f4f4f4;
    color: #333;
    margin: 0; /* Reset margin default */
}

.container {
    text-align: center;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    max-width: 600px; /* Batasi lebar maksimum */
    width: 90%; /* Lebar responsif */
     animation: fadeIn 1s ease; /* Tambahkan animasi */
}

h1 {
    font-size: 3rem;
    color: #663399;
    margin-bottom: 15px;
     animation: pulse 2s infinite;
}

p {
    font-size: 1.2rem;
    margin-bottom: 20px;
    line-height: 1.5; /* Improved readability */
}

/* Animasi putar untuk ikon hourglass */
/* Hapus .animated-hourglass */
#hourglass {
    color: #663399;
    margin-bottom: 25px;
    display: inline-block; /* Penting untuk animasi putar */
    animation: rotate 2s linear infinite; /* Pindahkan animasi ke sini */

}

.social-icons {
    margin-top: 30px;
}

.social-icons a {
    display: inline-block;
    margin: 0 10px;
    color: #663399;
    font-size: 2rem;
    transition: color 0.3s ease, transform 0.3s ease;
    text-decoration: none;

}

.social-icons a:hover {
    color: #8058a5;
    transform: scale(1.1);
}
/* Tombol Kembali ke Portfolio */
.back-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #663399;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 20px;
    transition: background-color 0.3s ease;
}

.back-button:hover {
    background-color: #8058a5;
}
.back-button i{
    margin-right: 8px;
}

/* Keyframes untuk animasi */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to   { opacity: 1; }
}