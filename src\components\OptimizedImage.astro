---
import { Image } from "astro:assets";
import type { ImageMetadata } from "astro";

export interface Props {
  src: ImageMetadata | string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: "lazy" | "eager";
  format?: "webp" | "avif" | "png" | "jpg";
  quality?: number;
  sizes?: string;
  densities?: number[];
}

const {
  src,
  alt,
  width,
  height,
  class: className = "",
  loading = "lazy",
  format = "webp",
  quality = 85,
  sizes,
  densities,
  ...rest
} = Astro.props;

// Generate responsive sizes if not provided
const defaultSizes = sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw";
---

<Image
  src={src}
  alt={alt}
  width={width}
  height={height}
  class={`transition-opacity duration-300 ${className}`}
  loading={loading}
  format={format}
  quality={quality}
  sizes={defaultSizes}
  densities={densities}
  {...rest}
/>

<style>
  img {
    @apply object-cover;
  }
  
  img[loading="lazy"] {
    opacity: 0;
    animation: fadeIn 0.3s ease-in-out forwards;
  }
  
  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }
</style>
