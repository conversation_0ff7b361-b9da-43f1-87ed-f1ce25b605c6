---
// Lazy Loading Image Component with performance optimizations
export interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  class?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  sizes?: string;
  srcset?: string;
  placeholder?: string;
  blurDataURL?: string;
  priority?: boolean;
}

const {
  src,
  alt,
  width,
  height,
  class: className = '',
  loading = 'lazy',
  decoding = 'async',
  sizes,
  srcset,
  placeholder,
  blurDataURL,
  priority = false
} = Astro.props;

// Generate responsive srcset if not provided
const generateSrcSet = (baseSrc: string) => {
  if (srcset) return srcset;
  
  const ext = baseSrc.split('.').pop();
  const baseName = baseSrc.replace(`.${ext}`, '');
  
  return [
    `${baseName}-400w.${ext} 400w`,
    `${baseName}-800w.${ext} 800w`,
    `${baseName}-1200w.${ext} 1200w`,
    `${baseName}-1600w.${ext} 1600w`
  ].join(', ');
};

// Generate sizes attribute if not provided
const generateSizes = () => {
  if (sizes) return sizes;
  return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
};

// Generate blur placeholder
const generateBlurPlaceholder = () => {
  if (blurDataURL) return blurDataURL;
  
  // Generate a simple blur placeholder SVG
  const svg = `
    <svg width="${width || 400}" height="${height || 300}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#2a2a2a;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)" />
      <circle cx="50%" cy="50%" r="20" fill="#00ffff" opacity="0.3" />
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

const placeholderSrc = placeholder || generateBlurPlaceholder();
---

<div 
  class={`lazy-image-container ${className}`}
  data-lazy-image
  style={`aspect-ratio: ${width && height ? `${width}/${height}` : 'auto'}`}
>
  <!-- Blur placeholder -->
  <img
    src={placeholderSrc}
    alt=""
    class="lazy-image-placeholder"
    style={`
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: blur(10px);
      transition: opacity 0.3s ease;
      z-index: 1;
    `}
    aria-hidden="true"
  />
  
  <!-- Main image -->
  <img
    src={priority ? src : placeholderSrc}
    data-src={priority ? undefined : src}
    srcset={priority ? generateSrcSet(src) : undefined}
    data-srcset={priority ? undefined : generateSrcSet(src)}
    sizes={generateSizes()}
    alt={alt}
    width={width}
    height={height}
    loading={priority ? 'eager' : loading}
    decoding={decoding}
    class="lazy-image-main"
    style={`
      position: relative;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: ${priority ? '1' : '0'};
      transition: opacity 0.3s ease;
      z-index: 2;
    `}
  />
  
  <!-- Loading indicator -->
  <div 
    class="lazy-image-loading"
    style={`
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 3;
      opacity: ${priority ? '0' : '1'};
      transition: opacity 0.3s ease;
    `}
  >
    <div class="loading-spinner">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 12a9 9 0 11-6.219-8.56"/>
      </svg>
    </div>
  </div>
</div>

<style>
  .lazy-image-container {
    position: relative;
    overflow: hidden;
    background: #1a1a1a;
    border-radius: 0.5rem;
  }
  
  .loading-spinner svg {
    animation: spin 1s linear infinite;
    color: #00ffff;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  .lazy-image-loaded .lazy-image-placeholder {
    opacity: 0;
  }
  
  .lazy-image-loaded .lazy-image-main {
    opacity: 1;
  }
  
  .lazy-image-loaded .lazy-image-loading {
    opacity: 0;
  }
  
  /* Error state */
  .lazy-image-error .lazy-image-main {
    opacity: 0;
  }
  
  .lazy-image-error .lazy-image-placeholder {
    opacity: 1;
    filter: grayscale(1) brightness(0.5);
  }
  
  .lazy-image-error::after {
    content: '⚠️ Failed to load image';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ff4444;
    font-size: 0.875rem;
    text-align: center;
    z-index: 4;
  }
</style>

<script>
  // Lazy loading implementation
  class LazyImageLoader {
    constructor() {
      this.observer = null;
      this.images = [];
      this.init();
    }

    init() {
      // Check if Intersection Observer is supported
      if ('IntersectionObserver' in window) {
        this.setupIntersectionObserver();
      } else {
        // Fallback for older browsers
        this.loadAllImages();
      }

      // Find all lazy images
      this.findImages();
    }

    setupIntersectionObserver() {
      const options = {
        root: null,
        rootMargin: '50px 0px', // Start loading 50px before image enters viewport
        threshold: 0.01
      };

      this.observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.loadImage(entry.target);
            this.observer.unobserve(entry.target);
          }
        });
      }, options);
    }

    findImages() {
      const containers = document.querySelectorAll('[data-lazy-image]');
      containers.forEach((container) => {
        const img = container.querySelector('.lazy-image-main');
        if (img && img.dataset.src) {
          if (this.observer) {
            this.observer.observe(container);
          } else {
            this.loadImage(container);
          }
        }
      });
    }

    loadImage(container) {
      const img = container.querySelector('.lazy-image-main');
      const placeholder = container.querySelector('.lazy-image-placeholder');
      const loading = container.querySelector('.lazy-image-loading');

      if (!img || !img.dataset.src) return;

      // Create a new image to preload
      const imageLoader = new Image();
      
      imageLoader.onload = () => {
        // Image loaded successfully
        img.src = img.dataset.src;
        if (img.dataset.srcset) {
          img.srcset = img.dataset.srcset;
        }
        
        // Mark as loaded
        container.classList.add('lazy-image-loaded');
        
        // Clean up data attributes
        delete img.dataset.src;
        delete img.dataset.srcset;
        
        // Dispatch custom event
        container.dispatchEvent(new CustomEvent('imageLoaded', {
          detail: { src: img.src }
        }));
      };

      imageLoader.onerror = () => {
        // Image failed to load
        container.classList.add('lazy-image-error');
        
        // Dispatch custom event
        container.dispatchEvent(new CustomEvent('imageError', {
          detail: { src: img.dataset.src }
        }));
      };

      // Start loading
      imageLoader.src = img.dataset.src;
    }

    loadAllImages() {
      // Fallback: load all images immediately
      const containers = document.querySelectorAll('[data-lazy-image]');
      containers.forEach((container) => {
        this.loadImage(container);
      });
    }

    // Public method to manually load an image
    loadImageById(id) {
      const container = document.getElementById(id);
      if (container) {
        this.loadImage(container);
      }
    }

    // Clean up
    destroy() {
      if (this.observer) {
        this.observer.disconnect();
      }
    }
  }

  // Initialize lazy loading when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.lazyImageLoader = new LazyImageLoader();
  });

  // Re-initialize for dynamically added images
  document.addEventListener('imagesAdded', () => {
    if (window.lazyImageLoader) {
      window.lazyImageLoader.findImages();
    }
  });

  // Performance monitoring
  if (window.performanceMonitor) {
    document.addEventListener('imageLoaded', (e) => {
      window.performanceMonitor.mark(`image-loaded-${Date.now()}`);
    });

    document.addEventListener('imageError', (e) => {
      console.warn('Image failed to load:', e.detail.src);
    });
  }
</script>
