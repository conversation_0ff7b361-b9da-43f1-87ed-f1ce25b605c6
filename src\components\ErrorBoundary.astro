---
export interface Props {
  fallback?: string;
}

const {
  fallback = "Something went wrong. Please try again later."
} = Astro.props;
---

<div 
  class="error-boundary bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center"
  role="alert"
  aria-live="assertive"
>
  <div class="space-y-4">
    <div class="text-red-400 text-4xl mb-4">
      ⚠️
    </div>
    
    <h2 class="text-xl font-semibold text-red-400">
      Oops! Something went wrong
    </h2>
    
    <p class="text-gray-300 text-sm">
      {fallback}
    </p>
    
    <button 
      onclick="window.location.reload()"
      class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-gray-900"
      type="button"
    >
      Reload Page
    </button>
  </div>
</div>

<style>
  .error-boundary {
    animation: fadeIn 0.3s ease-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
