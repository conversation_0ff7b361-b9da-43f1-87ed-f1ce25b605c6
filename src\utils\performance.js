// Performance Monitoring and Optimization Utilities

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      loadTime: 0,
      firstPaint: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      cumulativeLayoutShift: 0,
      firstInputDelay: 0,
      timeToInteractive: 0
    };
    
    this.observers = [];
    this.init();
  }

  init() {
    this.measureLoadTime();
    this.measurePaintMetrics();
    this.measureLCP();
    this.measureCLS();
    this.measureFID();
    this.measureTTI();
    this.setupResourceMonitoring();
  }

  measureLoadTime() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      this.reportMetric('loadTime', this.metrics.loadTime);
    });
  }

  measurePaintMetrics() {
    const paintEntries = performance.getEntriesByType('paint');
    
    paintEntries.forEach(entry => {
      if (entry.name === 'first-paint') {
        this.metrics.firstPaint = entry.startTime;
        this.reportMetric('firstPaint', this.metrics.firstPaint);
      } else if (entry.name === 'first-contentful-paint') {
        this.metrics.firstContentfulPaint = entry.startTime;
        this.reportMetric('firstContentfulPaint', this.metrics.firstContentfulPaint);
      }
    });
  }

  measureLCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
        this.reportMetric('largestContentfulPaint', this.metrics.largestContentfulPaint);
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    }
  }

  measureCLS() {
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        this.metrics.cumulativeLayoutShift = clsValue;
        this.reportMetric('cumulativeLayoutShift', this.metrics.cumulativeLayoutShift);
      });
      
      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    }
  }

  measureFID() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
          this.reportMetric('firstInputDelay', this.metrics.firstInputDelay);
        }
      });
      
      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    }
  }

  measureTTI() {
    // Simplified TTI measurement
    const checkTTI = () => {
      const navigation = performance.getEntriesByType('navigation')[0];
      if (navigation && navigation.domInteractive) {
        this.metrics.timeToInteractive = navigation.domInteractive;
        this.reportMetric('timeToInteractive', this.metrics.timeToInteractive);
      }
    };

    if (document.readyState === 'complete') {
      checkTTI();
    } else {
      window.addEventListener('load', checkTTI);
    }
  }

  setupResourceMonitoring() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.analyzeResource(entry);
        }
      });
      
      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    }
  }

  analyzeResource(entry) {
    const resourceData = {
      name: entry.name,
      type: entry.initiatorType,
      size: entry.transferSize,
      duration: entry.duration,
      startTime: entry.startTime
    };

    // Check for slow resources
    if (entry.duration > 1000) {
      console.warn('Slow resource detected:', resourceData);
    }

    // Check for large resources
    if (entry.transferSize > 1024 * 1024) { // 1MB
      console.warn('Large resource detected:', resourceData);
    }
  }

  reportMetric(name, value) {
    // Send to analytics or monitoring service
    console.log(`Performance Metric - ${name}:`, value);
    
    // You can integrate with services like Google Analytics, DataDog, etc.
    if (window.gtag) {
      window.gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: Math.round(value),
        custom_parameter: 'portfolio_performance'
      });
    }
  }

  getMetrics() {
    return { ...this.metrics };
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Lazy Loading Utilities
class LazyLoader {
  constructor() {
    this.imageObserver = null;
    this.componentObserver = null;
    this.init();
  }

  init() {
    this.setupImageLazyLoading();
    this.setupComponentLazyLoading();
  }

  setupImageLazyLoading() {
    if ('IntersectionObserver' in window) {
      this.imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            this.loadImage(img);
            this.imageObserver.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      this.observeImages();
    }
  }

  setupComponentLazyLoading() {
    if ('IntersectionObserver' in window) {
      this.componentObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const component = entry.target;
            this.loadComponent(component);
            this.componentObserver.unobserve(component);
          }
        });
      }, {
        rootMargin: '100px 0px',
        threshold: 0.01
      });

      this.observeComponents();
    }
  }

  observeImages() {
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => {
      this.imageObserver.observe(img);
    });
  }

  observeComponents() {
    const lazyComponents = document.querySelectorAll('[data-lazy-component]');
    lazyComponents.forEach(component => {
      this.componentObserver.observe(component);
    });
  }

  loadImage(img) {
    const src = img.dataset.src;
    const srcset = img.dataset.srcset;
    
    if (src) {
      img.src = src;
      img.removeAttribute('data-src');
    }
    
    if (srcset) {
      img.srcset = srcset;
      img.removeAttribute('data-srcset');
    }
    
    img.classList.add('loaded');
  }

  loadComponent(component) {
    const componentName = component.dataset.lazyComponent;
    
    if (componentName) {
      import(`../components/${componentName}.js`)
        .then(module => {
          const ComponentClass = module.default;
          new ComponentClass(component);
          component.classList.add('loaded');
        })
        .catch(error => {
          console.error(`Failed to load component ${componentName}:`, error);
        });
    }
  }

  destroy() {
    if (this.imageObserver) {
      this.imageObserver.disconnect();
    }
    if (this.componentObserver) {
      this.componentObserver.disconnect();
    }
  }
}

// Cache Management
class CacheManager {
  constructor() {
    this.cacheName = 'portfolio-cache-v1';
    this.init();
  }

  async init() {
    if ('serviceWorker' in navigator) {
      try {
        await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered successfully');
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  async cacheResources(resources) {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName);
        await cache.addAll(resources);
        console.log('Resources cached successfully');
      } catch (error) {
        console.error('Failed to cache resources:', error);
      }
    }
  }

  async getCachedResource(url) {
    if ('caches' in window) {
      try {
        const cache = await caches.open(this.cacheName);
        const response = await cache.match(url);
        return response;
      } catch (error) {
        console.error('Failed to get cached resource:', error);
        return null;
      }
    }
    return null;
  }

  async clearCache() {
    if ('caches' in window) {
      try {
        await caches.delete(this.cacheName);
        console.log('Cache cleared successfully');
      } catch (error) {
        console.error('Failed to clear cache:', error);
      }
    }
  }
}

// Export utilities
export { PerformanceMonitor, LazyLoader, CacheManager };

// Auto-initialize performance monitoring
if (typeof window !== 'undefined') {
  window.performanceMonitor = new PerformanceMonitor();
  window.lazyLoader = new LazyLoader();
  window.cacheManager = new CacheManager();
}
