/* style.css (Marketplace) */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #663399;
    --secondary-color: #8058a5;
    --text-color: #333;
    --background-color: #f4f4f4;
    --light-gray: #eee;
    --success-color: #2ecc71;
    --error-color: #e74c3c;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo {
    font-weight: bold;
    font-size: 1.2rem;
}

.nav-links {
    list-style: none;
    display: flex;
    gap: 20px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    padding: 8px 15px;
    border-radius: 5px;
    transition: background-color 0.3s ease, color 0.3s ease;
    display: block;
}

.nav-links a:hover {
    background-color: var(--primary-color);
    color: #fff;
}

.hamburger-menu {
    display: none;
    cursor: pointer;
    font-size: 1.5rem;
    padding: 10px;
    z-index: 1000;
}

.hamburger-menu i {
    color: black;
    transition: transform 0.3s ease;
}

main {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
}

section {
    margin-bottom: 40px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

section h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    font-size: 2rem;
}

section p {
    text-align: justify;
    line-height: 1.8;
}

.produk-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.produk {
    border: 1px solid var(--light-gray);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

.produk:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.produk img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin-bottom: 10px;
    order: -1;
}

.produk h3 {
    font-size: 1.25rem;
    margin-bottom: 5px;
    color: var(--primary-color);
    line-height: 1.2;
    white-space: normal;
    word-break: break-word;
}

.produk p {
    margin-bottom: 10px;
    font-size: 1rem;
    flex-grow: 1;
}

.produk .harga {
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.cta-button {
    display: inline-block;
    background-color: var(--primary-color);
    color: #fff;
    padding: 10px 15px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
    margin-top: auto;
    width: 100%;
}

.cta-button:hover {
    background-color: var(--secondary-color);
}

.contact-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: nowrap;
    padding: 0 10px;
}

.contact-links a {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    padding: 5px;
    transition: color 0.3s ease;
    padding-top: 1.5rem;
}

.contact-links a:hover {
    color: var(--secondary-color);
}

.contact-links i {
    font-size: 1.5rem;
    margin-right: 8px;
}

.modal-popup {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    background-color: #fff;
    max-width: 600px;
    margin: 50px auto;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    animation: slideDown 0.3s ease;
}

.close-button {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.8rem;
    color: var(--text-color);
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-button:hover {
    color: var(--primary-color);
}

.modal-content h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.8rem;
}

form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

form input[type="text"],
form input[type="email"],
form input[type="tel"],
form input[type="number"],
form textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid var(--light-gray);
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
}

form input[type="text"]:focus,
form input[type="email"]:focus,
form input[type="number"]:focus,
form input[type="tel"]:focus,
form textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 5px rgba(102, 51, 153, 0.5);
}

form textarea {
    resize: vertical;
    min-height: 100px;
}

#total-pembayaran {
    font-weight: bold;
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

#modal-produk .modal-content img {
    max-width: 70%;
    max-height: 60vh;
    width: auto;
    height: auto;
    display: block;
    margin: 5px auto;
}

footer {
    background-color: var(--primary-color);
    color: #fff;
    text-align: center;
    padding: 20px 0;
}

.error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: -10px;
    margin-bottom: 10px;
    display: block;
}

.modal-produk {
    z-index: 1001;
}

#contact-us .center-text {
    text-align: center;
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
        flex-direction: column;
        width: 100%;
        position: absolute;
        top: 68px;
        left: 0;
        background-color: #fff;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .nav-links.show {
        display: flex;
    }

    .nav-links li {
        margin: 0;
        text-align: center;
    }

    .nav-links a {
        padding: 15px;
        border-bottom: 1px solid var(--light-gray);
    }

    .hamburger-menu {
        display: block;
        margin-right: 0.9rem;
    }

    .modal-content {
        margin: 20px;
        padding: 20px;
        width: auto;
    }

    .produk h3 {
        font-size: 1.1rem;
    }

    .contact-links {
        gap: 15px;
        padding: 10px;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}