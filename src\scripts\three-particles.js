import * as THREE from "three";
import PostProcessingManager from "./post-processing.js";

class ParticleSystem {
  constructor(container) {
    this.container = container;
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.particles = null;
    this.mouse = new THREE.Vector2();
    this.mouseTarget = new THREE.Vector2();
    this.windowHalfX = window.innerWidth / 2;
    this.windowHalfY = window.innerHeight / 2;
    this.clock = new THREE.Clock();
    this.uniforms = {
      time: { value: 0 },
      mouse: { value: new THREE.Vector2() },
      resolution: {
        value: new THREE.Vector2(window.innerWidth, window.innerHeight),
      },
    };

    this.init();
    this.animate();
    this.addEventListeners();
  }

  init() {
    // Scene setup
    this.scene = new THREE.Scene();

    // Camera setup
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      1,
      3000
    );
    this.camera.position.z = 1000;

    // Renderer setup
    this.renderer = new THREE.WebGLRenderer({
      alpha: true,
      antialias: true,
      powerPreference: "high-performance",
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.container.appendChild(this.renderer.domElement);

    // Create particles
    this.createParticles();

    // Initialize post-processing
    this.postProcessing = new PostProcessingManager(
      this.renderer,
      this.scene,
      this.camera
    );
  }

  createParticles() {
    const particleCount = 1000;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);
    const velocities = new Float32Array(particleCount * 3);

    // Cyberpunk color palette
    const colorPalette = [
      new THREE.Color(0x00ffff), // Cyan
      new THREE.Color(0xff00ff), // Magenta
      new THREE.Color(0x00ff00), // Green
      new THREE.Color(0x8a2be2), // Purple
      new THREE.Color(0xff6600), // Orange
    ];

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;

      // Random positions
      positions[i3] = (Math.random() - 0.5) * 2000;
      positions[i3 + 1] = (Math.random() - 0.5) * 2000;
      positions[i3 + 2] = (Math.random() - 0.5) * 2000;

      // Random colors from palette
      const color =
        colorPalette[Math.floor(Math.random() * colorPalette.length)];
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;

      // Random sizes
      sizes[i] = Math.random() * 3 + 1;
    }

    geometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute("color", new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute("size", new THREE.BufferAttribute(sizes, 1));

    geometry.setAttribute("velocity", new THREE.BufferAttribute(velocities, 3));

    // Advanced particle material with custom shader
    const material = new THREE.ShaderMaterial({
      uniforms: {
        ...this.uniforms,
        pointTexture: { value: this.createParticleTexture() },
      },
      vertexShader: `
        precision mediump float;

        attribute float size;
        attribute vec3 color;
        attribute vec3 velocity;

        varying vec3 vColor;
        varying float vAlpha;

        uniform float time;
        uniform vec2 mouse;
        uniform vec2 resolution;

        void main() {
          vColor = color;

          vec3 pos = position;

          // Advanced wave animation with multiple frequencies
          float wave1 = sin(time * 0.002 + position.x * 0.01) * 15.0;
          float wave2 = cos(time * 0.003 + position.y * 0.008) * 10.0;
          float wave3 = sin(time * 0.001 + position.z * 0.005) * 8.0;

          pos.y += wave1 + wave2;
          pos.x += wave2 + wave3;
          pos.z += wave1 + wave3;

          // Mouse interaction
          vec2 mouseInfluence = (mouse - 0.5) * 2.0;
          float mouseDistance = length(mouseInfluence);
          float influence = 1.0 / (1.0 + mouseDistance * 0.1);

          pos.xy += mouseInfluence * influence * 50.0;

          // Dynamic alpha based on distance and time
          float distanceFromCenter = length(pos.xy) / 1000.0;
          vAlpha = 1.0 - distanceFromCenter + sin(time * 0.005) * 0.3;

          // Mouse interaction
          vec2 mouseInfluence = mouse * 0.1;
          pos.xy += mouseInfluence * (1.0 - length(pos.xy) / 1000.0);

          vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
          gl_PointSize = size * (300.0 / -mvPosition.z);
          gl_Position = projectionMatrix * mvPosition;
        }
      `,
      fragmentShader: `
        precision mediump float;

        varying vec3 vColor;
        varying float vAlpha;

        uniform sampler2D pointTexture;
        uniform float time;

        void main() {
          // Use texture for particle shape
          vec4 textureColor = texture2D(pointTexture, gl_PointCoord);

          // Calculate distance from center for circular falloff
          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));
          float alpha = 1.0 - smoothstep(0.0, 0.5, distanceToCenter);

          // Combine texture alpha with calculated alpha and varying alpha
          alpha *= textureColor.a * vAlpha;

          // Add pulsing effect
          alpha *= 0.8 + 0.2 * sin(time * 0.003);

          // Enhanced glow effect
          vec3 finalColor = vColor * (1.0 + 0.5 * alpha);

          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      blending: THREE.AdditiveBlending,
      depthTest: false,
      transparent: true,
      vertexColors: true,
    });

    // Check for shader compilation errors
    material.onBeforeCompile = (shader) => {
      console.log("Compiling particle shaders...");
    };

    // Add error handling
    material.addEventListener("error", (event) => {
      console.error("Shader compilation error:", event);
    });

    this.particles = new THREE.Points(geometry, material);
    this.scene.add(this.particles);
  }

  addEventListeners() {
    window.addEventListener("resize", () => this.onWindowResize(), false);
    document.addEventListener(
      "mousemove",
      (event) => this.onMouseMove(event),
      false
    );
    document.addEventListener(
      "touchmove",
      (event) => this.onTouchMove(event),
      false
    );
  }

  onWindowResize() {
    this.windowHalfX = window.innerWidth / 2;
    this.windowHalfY = window.innerHeight / 2;

    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();

    this.renderer.setSize(window.innerWidth, window.innerHeight);

    // Update post-processing render targets
    if (this.postProcessing) {
      this.postProcessing.resize(window.innerWidth, window.innerHeight);
    }
  }

  onMouseMove(event) {
    this.mouseTarget.x = (event.clientX - this.windowHalfX) / this.windowHalfX;
    this.mouseTarget.y = -(event.clientY - this.windowHalfY) / this.windowHalfY;
  }

  onTouchMove(event) {
    if (event.touches.length === 1) {
      event.preventDefault();
      this.mouseTarget.x =
        (event.touches[0].pageX - this.windowHalfX) / this.windowHalfX;
      this.mouseTarget.y =
        -(event.touches[0].pageY - this.windowHalfY) / this.windowHalfY;
    }
  }

  animate() {
    requestAnimationFrame(() => this.animate());

    // Smooth mouse movement
    this.mouse.x += (this.mouseTarget.x - this.mouse.x) * 0.05;
    this.mouse.y += (this.mouseTarget.y - this.mouse.y) * 0.05;

    // Update uniforms
    const elapsedTime = this.clock.getElapsedTime();
    this.uniforms.time.value = elapsedTime;
    this.uniforms.mouse.value.set(this.mouse.x, this.mouse.y);

    if (this.particles && this.particles.material.uniforms) {
      this.particles.material.uniforms.time.value = elapsedTime;
      this.particles.material.uniforms.mouse.value = this.mouse;
    }

    // Rotate particles
    if (this.particles) {
      this.particles.rotation.x += 0.0005;
      this.particles.rotation.y += 0.001;
    }

    // Use post-processing render instead of direct render
    if (this.postProcessing) {
      this.postProcessing.render();
    } else {
      this.renderer.render(this.scene, this.camera);
    }
  }

  createParticleTexture() {
    const canvas = document.createElement("canvas");
    canvas.width = 64;
    canvas.height = 64;

    const context = canvas.getContext("2d");
    const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32);

    gradient.addColorStop(0, "rgba(255, 255, 255, 1)");
    gradient.addColorStop(0.2, "rgba(0, 255, 255, 0.8)");
    gradient.addColorStop(0.4, "rgba(255, 0, 255, 0.6)");
    gradient.addColorStop(1, "rgba(0, 0, 0, 0)");

    context.fillStyle = gradient;
    context.fillRect(0, 0, 64, 64);

    const texture = new THREE.CanvasTexture(canvas);
    texture.needsUpdate = true;

    return texture;
  }

  destroy() {
    if (this.renderer) {
      this.renderer.dispose();
      this.container.removeChild(this.renderer.domElement);
    }

    window.removeEventListener("resize", this.onWindowResize);
    document.removeEventListener("mousemove", this.onMouseMove);
    document.removeEventListener("touchmove", this.onTouchMove);
  }
}

export default ParticleSystem;
