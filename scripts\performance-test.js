#!/usr/bin/env node

/**
 * Performance Testing Script
 * Tests Core Web Vitals and other performance metrics
 */

import { chromium } from 'playwright';
import lighthouse from 'lighthouse';
import { launch } from 'chrome-launcher';
import fs from 'fs/promises';
import path from 'path';

const BASE_URL = 'http://localhost:4321';
const PAGES_TO_TEST = [
  { path: '/', name: 'Home' },
  { path: '/projects', name: 'Projects' },
];

const PERFORMANCE_THRESHOLDS = {
  lcp: 2500, // Largest Contentful Paint (ms)
  fid: 100,  // First Input Delay (ms)
  cls: 0.1,  // Cumulative Layout Shift
  fcp: 1800, // First Contentful Paint (ms)
  ttfb: 800, // Time to First Byte (ms)
  lighthouse: {
    performance: 90,
    accessibility: 95,
    bestPractices: 90,
    seo: 95,
  }
};

class PerformanceTester {
  constructor() {
    this.results = [];
  }

  async testPageWithPlaywright(url, pageName) {
    console.log(`🚀 Testing ${pageName} with Playwright...`);
    
    const browser = await chromium.launch({ headless: true });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    try {
      // Collect performance metrics
      const metrics = {};
      
      // Start timing
      const startTime = Date.now();
      
      // Navigate to page
      const response = await page.goto(url, { waitUntil: 'networkidle' });
      metrics.ttfb = response.headers()['server-timing'] || (Date.now() - startTime);
      
      // Wait for page to be fully loaded
      await page.waitForLoadState('networkidle');
      
      // Get Core Web Vitals using JavaScript
      const webVitals = await page.evaluate(() => {
        return new Promise((resolve) => {
          const vitals = {};
          
          // LCP (Largest Contentful Paint)
          new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            vitals.lcp = lastEntry.startTime;
          }).observe({ entryTypes: ['largest-contentful-paint'] });
          
          // FID (First Input Delay) - simulate with click
          document.addEventListener('click', (event) => {
            vitals.fid = performance.now() - event.timeStamp;
          }, { once: true });
          
          // CLS (Cumulative Layout Shift)
          let clsValue = 0;
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value;
              }
            }
            vitals.cls = clsValue;
          }).observe({ entryTypes: ['layout-shift'] });
          
          // FCP (First Contentful Paint)
          new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              if (entry.name === 'first-contentful-paint') {
                vitals.fcp = entry.startTime;
              }
            }
          }).observe({ entryTypes: ['paint'] });
          
          // Wait a bit for metrics to be collected
          setTimeout(() => {
            resolve(vitals);
          }, 3000);
        });
      });
      
      // Get additional performance metrics
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          domInteractive: navigation.domInteractive - navigation.fetchStart,
          firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
        };
      });
      
      // Get resource loading metrics
      const resourceMetrics = await page.evaluate(() => {
        const resources = performance.getEntriesByType('resource');
        const totalSize = resources.reduce((sum, resource) => {
          return sum + (resource.transferSize || 0);
        }, 0);
        
        return {
          totalResources: resources.length,
          totalSize: totalSize,
          imageCount: resources.filter(r => r.initiatorType === 'img').length,
          scriptCount: resources.filter(r => r.initiatorType === 'script').length,
          stylesheetCount: resources.filter(r => r.initiatorType === 'link').length,
        };
      });
      
      // Simulate user interaction for FID
      await page.click('body');
      await page.waitForTimeout(1000);
      
      const result = {
        url,
        pageName,
        timestamp: new Date().toISOString(),
        webVitals,
        performanceMetrics,
        resourceMetrics,
        passed: this.checkThresholds(webVitals, performanceMetrics),
      };
      
      console.log(`✅ ${pageName} Playwright testing completed`);
      return result;
      
    } catch (error) {
      console.error(`❌ Error testing ${pageName}:`, error.message);
      return null;
    } finally {
      await browser.close();
    }
  }

  async testPageWithLighthouse(url, pageName) {
    console.log(`🔍 Testing ${pageName} with Lighthouse...`);
    
    const chrome = await launch({ chromeFlags: ['--headless'] });
    
    try {
      const options = {
        logLevel: 'info',
        output: 'json',
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
        port: chrome.port,
      };
      
      const runnerResult = await lighthouse(url, options);
      
      if (!runnerResult) {
        throw new Error('Lighthouse failed to run');
      }
      
      const { lhr } = runnerResult;
      
      const result = {
        url,
        pageName,
        timestamp: new Date().toISOString(),
        scores: {
          performance: Math.round(lhr.categories.performance.score * 100),
          accessibility: Math.round(lhr.categories.accessibility.score * 100),
          bestPractices: Math.round(lhr.categories['best-practices'].score * 100),
          seo: Math.round(lhr.categories.seo.score * 100),
        },
        metrics: {
          firstContentfulPaint: lhr.audits['first-contentful-paint'].numericValue,
          largestContentfulPaint: lhr.audits['largest-contentful-paint'].numericValue,
          firstInputDelay: lhr.audits['max-potential-fid'].numericValue,
          cumulativeLayoutShift: lhr.audits['cumulative-layout-shift'].numericValue,
          speedIndex: lhr.audits['speed-index'].numericValue,
          timeToInteractive: lhr.audits['interactive'].numericValue,
        },
        opportunities: lhr.audits['opportunities'] ? 
          Object.keys(lhr.audits).filter(key => 
            lhr.audits[key].details && 
            lhr.audits[key].details.type === 'opportunity'
          ).map(key => ({
            id: key,
            title: lhr.audits[key].title,
            description: lhr.audits[key].description,
            score: lhr.audits[key].score,
            numericValue: lhr.audits[key].numericValue,
          })) : [],
        passed: this.checkLighthouseThresholds(lhr.categories),
      };
      
      console.log(`✅ ${pageName} Lighthouse testing completed`);
      return result;
      
    } catch (error) {
      console.error(`❌ Error running Lighthouse for ${pageName}:`, error.message);
      return null;
    } finally {
      await chrome.kill();
    }
  }

  checkThresholds(webVitals, performanceMetrics) {
    const checks = {
      lcp: (webVitals.lcp || 0) <= PERFORMANCE_THRESHOLDS.lcp,
      fid: (webVitals.fid || 0) <= PERFORMANCE_THRESHOLDS.fid,
      cls: (webVitals.cls || 0) <= PERFORMANCE_THRESHOLDS.cls,
      fcp: (webVitals.fcp || performanceMetrics.firstContentfulPaint || 0) <= PERFORMANCE_THRESHOLDS.fcp,
    };
    
    return {
      individual: checks,
      overall: Object.values(checks).every(Boolean),
    };
  }

  checkLighthouseThresholds(categories) {
    const checks = {
      performance: (categories.performance.score * 100) >= PERFORMANCE_THRESHOLDS.lighthouse.performance,
      accessibility: (categories.accessibility.score * 100) >= PERFORMANCE_THRESHOLDS.lighthouse.accessibility,
      bestPractices: (categories['best-practices'].score * 100) >= PERFORMANCE_THRESHOLDS.lighthouse.bestPractices,
      seo: (categories.seo.score * 100) >= PERFORMANCE_THRESHOLDS.lighthouse.seo,
    };
    
    return {
      individual: checks,
      overall: Object.values(checks).every(Boolean),
    };
  }

  async generateReport() {
    const reportData = {
      testRun: {
        timestamp: new Date().toISOString(),
        baseUrl: BASE_URL,
        pagesTestedCount: this.results.length,
        thresholds: PERFORMANCE_THRESHOLDS,
      },
      results: this.results,
      summary: this.generateSummary(),
    };
    
    // Ensure reports directory exists
    await fs.mkdir('reports', { recursive: true });
    
    // Save JSON report
    await fs.writeFile(
      path.join('reports', 'performance-report.json'),
      JSON.stringify(reportData, null, 2)
    );
    
    // Generate and save HTML report
    const htmlReport = this.generateHTMLReport(reportData);
    await fs.writeFile(
      path.join('reports', 'performance-report.html'),
      htmlReport
    );
    
    console.log('\n📊 Performance reports generated:');
    console.log('  - reports/performance-report.json');
    console.log('  - reports/performance-report.html');
  }

  generateSummary() {
    const summary = {
      totalPages: this.results.length,
      passedPages: 0,
      averageScores: {
        performance: 0,
        accessibility: 0,
        bestPractices: 0,
        seo: 0,
      },
      averageMetrics: {
        lcp: 0,
        fid: 0,
        cls: 0,
        fcp: 0,
      },
    };
    
    this.results.forEach(result => {
      if (result.playwright?.passed?.overall && result.lighthouse?.passed?.overall) {
        summary.passedPages++;
      }
      
      if (result.lighthouse) {
        summary.averageScores.performance += result.lighthouse.scores.performance;
        summary.averageScores.accessibility += result.lighthouse.scores.accessibility;
        summary.averageScores.bestPractices += result.lighthouse.scores.bestPractices;
        summary.averageScores.seo += result.lighthouse.scores.seo;
      }
      
      if (result.playwright) {
        summary.averageMetrics.lcp += result.playwright.webVitals.lcp || 0;
        summary.averageMetrics.fid += result.playwright.webVitals.fid || 0;
        summary.averageMetrics.cls += result.playwright.webVitals.cls || 0;
        summary.averageMetrics.fcp += result.playwright.webVitals.fcp || 0;
      }
    });
    
    // Calculate averages
    const count = this.results.length;
    if (count > 0) {
      Object.keys(summary.averageScores).forEach(key => {
        summary.averageScores[key] = Math.round(summary.averageScores[key] / count);
      });
      
      Object.keys(summary.averageMetrics).forEach(key => {
        summary.averageMetrics[key] = Math.round(summary.averageMetrics[key] / count);
      });
    }
    
    return summary;
  }

  generateHTMLReport(data) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f0f8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .page-result { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .metric { background: #f9f9f9; padding: 15px; border-radius: 8px; text-align: center; }
        .metric.good { background: #e6ffe6; }
        .metric.needs-improvement { background: #fff3cd; }
        .metric.poor { background: #ffe6e6; }
        .score { font-size: 2em; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Performance Test Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Test Date:</strong> ${data.testRun.timestamp}</p>
        <p><strong>Pages Tested:</strong> ${data.summary.totalPages}</p>
        <p><strong>Pages Passed:</strong> ${data.summary.passedPages}/${data.summary.totalPages}</p>
        <div class="metrics">
            <div class="metric">
                <div class="score">${data.summary.averageScores.performance}</div>
                <div>Performance</div>
            </div>
            <div class="metric">
                <div class="score">${data.summary.averageScores.accessibility}</div>
                <div>Accessibility</div>
            </div>
            <div class="metric">
                <div class="score">${data.summary.averageScores.bestPractices}</div>
                <div>Best Practices</div>
            </div>
            <div class="metric">
                <div class="score">${data.summary.averageScores.seo}</div>
                <div>SEO</div>
            </div>
        </div>
    </div>
    
    ${data.results.map(result => `
        <div class="page-result">
            <h2>${result.pageName}</h2>
            <p><strong>URL:</strong> ${result.url}</p>
            
            ${result.lighthouse ? `
                <h3>Lighthouse Scores</h3>
                <div class="metrics">
                    ${Object.entries(result.lighthouse.scores).map(([key, score]) => `
                        <div class="metric ${score >= 90 ? 'good' : score >= 70 ? 'needs-improvement' : 'poor'}">
                            <div class="score">${score}</div>
                            <div>${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}</div>
                        </div>
                    `).join('')}
                </div>
                
                <h3>Core Web Vitals</h3>
                <div class="metrics">
                    <div class="metric ${result.lighthouse.metrics.largestContentfulPaint <= 2500 ? 'good' : 'poor'}">
                        <div class="score">${Math.round(result.lighthouse.metrics.largestContentfulPaint)}ms</div>
                        <div>LCP</div>
                    </div>
                    <div class="metric ${result.lighthouse.metrics.firstInputDelay <= 100 ? 'good' : 'poor'}">
                        <div class="score">${Math.round(result.lighthouse.metrics.firstInputDelay)}ms</div>
                        <div>FID</div>
                    </div>
                    <div class="metric ${result.lighthouse.metrics.cumulativeLayoutShift <= 0.1 ? 'good' : 'poor'}">
                        <div class="score">${result.lighthouse.metrics.cumulativeLayoutShift.toFixed(3)}</div>
                        <div>CLS</div>
                    </div>
                </div>
            ` : ''}
        </div>
    `).join('')}
</body>
</html>`;
  }

  async run() {
    console.log('🚀 Starting performance tests...\n');
    
    for (const page of PAGES_TO_TEST) {
      const url = `${BASE_URL}${page.path}`;
      
      console.log(`\n📄 Testing ${page.name}...`);
      
      const playwrightResult = await this.testPageWithPlaywright(url, page.name);
      const lighthouseResult = await this.testPageWithLighthouse(url, page.name);
      
      this.results.push({
        url,
        pageName: page.name,
        timestamp: new Date().toISOString(),
        playwright: playwrightResult,
        lighthouse: lighthouseResult,
      });
    }
    
    await this.generateReport();
    
    const summary = this.generateSummary();
    
    console.log('\n🎯 Performance Test Summary:');
    console.log(`   Pages Passed: ${summary.passedPages}/${summary.totalPages}`);
    console.log(`   Average Performance Score: ${summary.averageScores.performance}`);
    console.log(`   Average LCP: ${summary.averageMetrics.lcp}ms`);
    console.log(`   Average CLS: ${summary.averageMetrics.cls}`);
    
    if (summary.passedPages < summary.totalPages) {
      console.log('\n⚠️  Some pages failed performance thresholds.');
      process.exit(1);
    } else {
      console.log('\n✅ All pages passed performance thresholds!');
    }
  }
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new PerformanceTester();
  tester.run().catch(error => {
    console.error('❌ Performance test failed:', error);
    process.exit(1);
  });
}

export default PerformanceTester;
