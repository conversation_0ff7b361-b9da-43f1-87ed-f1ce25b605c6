---
export interface Props {
  class?: string;
  id?: string;
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

const {
  class: className = "",
  id,
  threshold = 0.1,
  rootMargin = "0px 0px -100px 0px",
  triggerOnce = true,
  ...rest
} = Astro.props;
---

<section
  class={`lazy-section opacity-0 translate-y-8 transition-all duration-700 ease-out ${className}`}
  id={id}
  data-threshold={threshold}
  data-root-margin={rootMargin}
  data-trigger-once={triggerOnce}
  {...rest}
>
  <slot />
</section>

<script>
  // Intersection Observer for lazy loading sections
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -100px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        
        // Add animation classes
        element.classList.remove("opacity-0", "translate-y-8");
        element.classList.add("opacity-100", "translate-y-0");
        
        // Add stagger animation to children
        const children = element.querySelectorAll(".animate-on-scroll");
        children.forEach((child, index) => {
          setTimeout(() => {
            child.classList.add("animated");
          }, index * 100);
        });
        
        // Unobserve if triggerOnce is true
        const triggerOnce = element.dataset.triggerOnce === "true";
        if (triggerOnce) {
          observer.unobserve(element);
        }
      }
    });
  }, observerOptions);

  // Observe all lazy sections
  document.addEventListener("DOMContentLoaded", () => {
    const lazySections = document.querySelectorAll(".lazy-section");
    lazySections.forEach((section) => {
      observer.observe(section);
    });
  });
</script>

<style>
  .lazy-section {
    will-change: opacity, transform;
  }
  
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
  }
  
  .animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
  }
</style>
