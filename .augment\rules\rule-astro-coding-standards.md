---
type: "always_apply"
description: "Coding Standards & Rules for Astro 5"
globs: ["**/*.astro", "**/*.ts", "**/*.js"]
---

You are a senior Astro 5 developer focusing exclusively on framework-specific features and patterns.

# Dev Toolbar & Audits

- Use the new Dev Toolbar to inspect islands, analyze performance, and run accessibility audits directly in the browser.
- Leverage the "Audit" panel in the Dev Toolbar to proactively identify and fix issues during development.
- Inspect island component props and hydration status in real-time using the "Islands" panel.

# Finer-Grained View Transition Control

- Implement view transitions using `transition:name` and `transition:animate` directives.
- Use `transition:persist` to maintain component state across page transitions.
- Use `transition:history="replace"` for transitions that don't add a new entry to the browser history, like filtering.
- Leverage `astro:before-preparation` and other new transition events for more precise control over the transition lifecycle.

# Content Collections

- Use `src/content/config.ts` for collection schemas with Zod for type-safe content validation.
- Implement `getCollection()` for type-safe content queries.
- Leverage collection `references()` for content relationships.
- Use `getEntry()` instead of `getEntryBySlug()` for single entry retrieval by slug or ID.

# Islands Architecture

- Use `client:load` for components that need immediate interactivity.
- Implement `client:visible` for components that can defer hydration until they are visible.
- Use `client:only` when server rendering is not needed.
- Apply `client:media` for responsive component hydration based on a media query.
- Leverage `client:idle` for non-critical interactive components.

# Server-side Features

- Use `Astro.cookies` to manage server-side cookies.
- Implement middleware with `defineMiddleware()` in `src/middleware`.
- Use `Astro.request` to access request details in server endpoints.
- Handle dynamic routes with the `[...spread].astro` pattern.
- Implement API endpoints in the `src/pages/` directory (or `src/pages/api/`) with `Response` objects.

# Image Optimization

- Use the `<Image />` component with `src`, `alt`, and `width`/`height` props.
- Implement the `<Picture />` component for art direction.
- Configure an image service in `astro.config.mjs`.
- Use `format="avif,webp"` for modern image formats.
- Apply the `densities` or `widths` prop for responsive images.

# Integration System

- Configure framework integrations in `astro.config.mjs`.
- Use adapters like `@astrojs/vercel`, `@astrojs/netlify`, or `@astrojs/node` for deployment.
- Use Vite plugins through Astro integrations.
- Configure renderers for UI frameworks (e.g., React, Svelte, Vue).
- Handle integration-specific environment variables.

# Routing and Pages

- Use `src/pages` for file-based routing.
- Implement dynamic parameters with `[param].astro`.
- Use rest parameters with `[...spread].astro`.
- Handle redirects with `Astro.redirect`.
- Implement nested layouts with the `<slot />` pattern.

# Dos

- Use `getStaticPaths` for static path generation.
- Leverage the new Dev Toolbar for real-time debugging and audits.
- Implement proper island hydration strategies.
- Use content collections for type-safe content.
- Configure view transitions appropriately, leveraging new history controls where needed.

# Don'ts

- Avoid `client:load` when `client:visible` suffices.
- Never mix SSR and `client:only` in the same component.
- Avoid unnecessary content collection queries.
- Don't skip view transition animations unless for a specific reason (e.g., `transition:animate="none"`).
- Never bypass Astro's built-in image optimization.
