import * as THREE from 'three';

// Post-processing effects for enhanced 3D graphics
class PostProcessingManager {
  constructor(renderer, scene, camera) {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    this.composer = null;
    this.passes = {};
    
    this.init();
  }

  init() {
    // Create render target
    this.renderTarget = new THREE.WebGLRenderTarget(
      window.innerWidth,
      window.innerHeight,
      {
        minFilter: THREE.LinearFilter,
        magFilter: THREE.LinearFilter,
        format: THREE.RGBAFormat,
        stencilBuffer: false
      }
    );

    this.setupPasses();
    this.createComposer();
  }

  setupPasses() {
    // Bloom Pass
    this.passes.bloom = this.createBloomPass();
    
    // Film Pass (noise and scanlines)
    this.passes.film = this.createFilmPass();
    
    // Glitch Pass
    this.passes.glitch = this.createGlitchPass();
    
    // Color Correction Pass
    this.passes.colorCorrection = this.createColorCorrectionPass();
  }

  createBloomPass() {
    const bloomShader = {
      uniforms: {
        tDiffuse: { value: null },
        strength: { value: 1.5 },
        radius: { value: 0.4 },
        threshold: { value: 0.85 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float strength;
        uniform float radius;
        uniform float threshold;
        varying vec2 vUv;

        void main() {
          vec4 color = texture2D(tDiffuse, vUv);
          
          // Extract bright areas
          float brightness = dot(color.rgb, vec3(0.299, 0.587, 0.114));
          vec4 bright = color * step(threshold, brightness);
          
          // Simple bloom effect
          vec4 bloom = vec4(0.0);
          float samples = 9.0;
          
          for(float i = 0.0; i < samples; i++) {
            float angle = i * 6.28318 / samples;
            vec2 offset = vec2(cos(angle), sin(angle)) * radius * 0.01;
            bloom += texture2D(tDiffuse, vUv + offset) * 0.111;
          }
          
          gl_FragColor = color + bloom * bright * strength;
        }
      `
    };

    return new THREE.ShaderMaterial(bloomShader);
  }

  createFilmPass() {
    const filmShader = {
      uniforms: {
        tDiffuse: { value: null },
        time: { value: 0 },
        nIntensity: { value: 0.5 },
        sIntensity: { value: 0.05 },
        sCount: { value: 4096 },
        grayscale: { value: 0 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float time;
        uniform float nIntensity;
        uniform float sIntensity;
        uniform float sCount;
        uniform int grayscale;
        varying vec2 vUv;

        void main() {
          vec4 cTextureScreen = texture2D(tDiffuse, vUv);
          
          // Noise
          float x = vUv.x * vUv.y * time * 1000.0;
          x = mod(x, 13.0) * mod(x, 123.0);
          float dx = mod(x, 0.01);
          vec3 cResult = cTextureScreen.rgb + cTextureScreen.rgb * clamp(0.1 + dx * 100.0, 0.0, 1.0);
          
          // Scanlines
          vec2 sc = vec2(sin(vUv.y * sCount), cos(vUv.y * sCount));
          cResult += cTextureScreen.rgb * vec3(sc.x, sc.y, sc.x) * sIntensity;
          
          // Interpolate between original and result
          cResult = cTextureScreen.rgb + clamp(nIntensity, 0.0, 1.0) * (cResult - cTextureScreen.rgb);
          
          // Grayscale
          if(grayscale == 1) {
            cResult = vec3(cResult.r * 0.3 + cResult.g * 0.59 + cResult.b * 0.11);
          }
          
          gl_FragColor = vec4(cResult, cTextureScreen.a);
        }
      `
    };

    return new THREE.ShaderMaterial(filmShader);
  }

  createGlitchPass() {
    const glitchShader = {
      uniforms: {
        tDiffuse: { value: null },
        time: { value: 0 },
        distortion: { value: 0.3 },
        distortion2: { value: 5.0 },
        speed: { value: 0.2 },
        rollSpeed: { value: 0.1 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float time;
        uniform float distortion;
        uniform float distortion2;
        uniform float speed;
        uniform float rollSpeed;
        varying vec2 vUv;

        vec3 mod289(vec3 x) {
          return x - floor(x * (1.0 / 289.0)) * 289.0;
        }

        vec2 mod289(vec2 x) {
          return x - floor(x * (1.0 / 289.0)) * 289.0;
        }

        vec3 permute(vec3 x) {
          return mod289(((x*34.0)+1.0)*x);
        }

        float snoise(vec2 v) {
          const vec4 C = vec4(0.211324865405187, 0.366025403784439, -0.577350269189626, 0.024390243902439);
          vec2 i = floor(v + dot(v, C.yy));
          vec2 x0 = v - i + dot(i, C.xx);
          vec2 i1;
          i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
          vec4 x12 = x0.xyxy + C.xxzz;
          x12.xy -= i1;
          i = mod289(i);
          vec3 p = permute(permute(i.y + vec3(0.0, i1.y, 1.0)) + i.x + vec3(0.0, i1.x, 1.0));
          vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
          m = m*m;
          m = m*m;
          vec3 x = 2.0 * fract(p * C.www) - 1.0;
          vec3 h = abs(x) - 0.5;
          vec3 ox = floor(x + 0.5);
          vec3 a0 = x - ox;
          m *= 1.79284291400159 - 0.85373472095314 * (a0*a0 + h*h);
          vec3 g;
          g.x = a0.x * x0.x + h.x * x0.y;
          g.yz = a0.yz * x12.xz + h.yz * x12.yw;
          return 130.0 * dot(m, g);
        }

        void main() {
          vec2 uv = vUv;
          
          // Digital distortion
          float noise = snoise(vec2(time * speed, uv.y * 80.0)) * distortion;
          noise += snoise(vec2(time * speed * 2.0, uv.y * 200.0)) * distortion;
          
          // Horizontal displacement
          uv.x += noise * 0.05;
          
          // Vertical roll
          uv.y += snoise(vec2(time * rollSpeed)) * 0.01;
          
          // Color separation
          vec4 color;
          color.r = texture2D(tDiffuse, uv + vec2(0.01, 0.0)).r;
          color.g = texture2D(tDiffuse, uv).g;
          color.b = texture2D(tDiffuse, uv - vec2(0.01, 0.0)).b;
          color.a = texture2D(tDiffuse, uv).a;
          
          // Random glitch lines
          float glitch = step(0.9, snoise(vec2(time * 10.0, uv.y * 100.0)));
          color.rgb = mix(color.rgb, vec3(1.0, 0.0, 1.0), glitch * 0.1);
          
          gl_FragColor = color;
        }
      `
    };

    return new THREE.ShaderMaterial(glitchShader);
  }

  createColorCorrectionPass() {
    const colorShader = {
      uniforms: {
        tDiffuse: { value: null },
        brightness: { value: 0.1 },
        contrast: { value: 1.2 },
        saturation: { value: 1.3 },
        hue: { value: 0.0 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float brightness;
        uniform float contrast;
        uniform float saturation;
        uniform float hue;
        varying vec2 vUv;

        vec3 rgb2hsv(vec3 c) {
          vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
          vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
          vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
          float d = q.x - min(q.w, q.y);
          float e = 1.0e-10;
          return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
        }

        vec3 hsv2rgb(vec3 c) {
          vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
          vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
          return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
        }

        void main() {
          vec4 color = texture2D(tDiffuse, vUv);
          
          // Brightness
          color.rgb += brightness;
          
          // Contrast
          color.rgb = (color.rgb - 0.5) * contrast + 0.5;
          
          // Saturation and Hue
          vec3 hsv = rgb2hsv(color.rgb);
          hsv.y *= saturation;
          hsv.x += hue;
          color.rgb = hsv2rgb(hsv);
          
          gl_FragColor = color;
        }
      `
    };

    return new THREE.ShaderMaterial(colorShader);
  }

  createComposer() {
    // Create a simple composer using render targets
    this.renderTargets = [
      this.renderTarget.clone(),
      this.renderTarget.clone()
    ];
    
    // Create quad for full-screen passes
    this.quad = new THREE.Mesh(
      new THREE.PlaneGeometry(2, 2),
      null
    );
    this.quadScene = new THREE.Scene();
    this.quadScene.add(this.quad);
    this.quadCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);
  }

  render() {
    let currentTarget = 0;
    
    // Render scene to first target
    this.renderer.setRenderTarget(this.renderTargets[currentTarget]);
    this.renderer.render(this.scene, this.camera);
    
    // Apply post-processing passes
    const passes = [
      this.passes.bloom,
      this.passes.film,
      this.passes.glitch,
      this.passes.colorCorrection
    ];
    
    passes.forEach((pass, index) => {
      const isLastPass = index === passes.length - 1;
      const inputTarget = this.renderTargets[currentTarget];
      const outputTarget = isLastPass ? null : this.renderTargets[1 - currentTarget];
      
      // Set input texture
      pass.uniforms.tDiffuse.value = inputTarget.texture;
      
      // Update time-based uniforms
      if (pass.uniforms.time) {
        pass.uniforms.time.value = performance.now() * 0.001;
      }
      
      // Set material and render
      this.quad.material = pass;
      this.renderer.setRenderTarget(outputTarget);
      this.renderer.render(this.quadScene, this.quadCamera);
      
      currentTarget = 1 - currentTarget;
    });
  }

  resize(width, height) {
    this.renderTargets.forEach(target => {
      target.setSize(width, height);
    });
  }

  dispose() {
    this.renderTargets.forEach(target => target.dispose());
    Object.values(this.passes).forEach(pass => pass.dispose());
  }
}

export default PostProcessingManager;
